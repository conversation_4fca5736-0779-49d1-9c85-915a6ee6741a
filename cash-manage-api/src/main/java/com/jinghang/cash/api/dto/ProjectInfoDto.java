package com.jinghang.cash.api.dto;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.api.enums.ProjectType;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 项目信息VO
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:57
 */
@Data
public class ProjectInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 项目唯一编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    private ProjectType projectTypeCode;

    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    private AbleStatus enabled;

    /**
     * 项目开始日期
     */
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    private LocalDate endDate;

    /**
     * 项目要素信息
     */
    private ProjectElementsDto elements;

    /**
     * 项目要素扩展信息
     */
    private ProjectElementsExtDto elementsExt;
}
