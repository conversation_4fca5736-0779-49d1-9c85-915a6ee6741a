<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.FlowConfigSlaveMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.FlowConfigSlave">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="flowChannel" column="flow_channel" jdbcType="VARCHAR"/>
            <result property="creditDayAmt" column="credit_day_amt" jdbcType="DECIMAL"/>
            <result property="loanDayAmt" column="loan_day_amt" jdbcType="DECIMAL"/>
            <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,flow_channel,credit_day_amt,
        loan_day_amt,enabled,remark,
        revision,created_by,created_time,
        updated_by,updated_time
    </sql>
</mapper>
