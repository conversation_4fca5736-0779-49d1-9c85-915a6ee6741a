<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.cash.modules.project.mapper.CapitalConfigMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.CapitalConfig">
        <id column="id" property="id"/>
        <result column="bank_channel" property="bankChannel"/>
        <result column="enabled" property="enabled"/>
        <result column="remark" property="remark"/>
        <result column="revision" property="revision"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="capital_name_short" property="capitalNameShort"/>
        <result column="capital_name" property="capitalName"/>
        <result column="capital_introduction" property="capitalIntroduction"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="email_address" property="emailAddress"/>
        <result column="main_prod" property="mainProd"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, bank_channel, enabled, remark, revision, created_by, created_time, updated_by, updated_time, capital_name_short, capital_name, capital_introduction, contact_person, contact_phone, email_address, main_prod
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from capital_config
        <where>
            <if test="criteria.bankChannel != null">
                and bank_channel = #{criteria.bankChannel}
            </if>
            <if test="criteria.enabled != null">
                and enabled = #{criteria.enabled}
            </if>
            <if test="criteria.capitalName != null">
                and capital_name like concat('%',#{criteria.capitalName},'%')
            </if>
        </where>
        order by id desc
    </select>
</mapper>