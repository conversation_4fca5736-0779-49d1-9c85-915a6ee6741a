package com.jinghang.cash.modules.project.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jinghang.cash.api.enums.*;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;


/**
 * 项目要素实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:39
 */
@TableName(value = "project_elements")
@Getter
@Setter
public class ProjectElements implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 关联的项目唯一编码
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 可提现范围(元) (格式 如 1000-50000)
     */
    @TableField(value = "drawable_amount_range")
    private String drawableAmountRange;

    /**
     * 单笔提现步长(元)
     */
    @TableField(value = "drawable_amount_step")
    private String drawableAmountStep;

    /**
     * 授信黑暗期 (格式 HH:mm-HH:mm)
     */
    @TableField(value = "credit_dark_hours")
    private String creditDarkHours;

    /**
     * 用信黑暗期 (格式 HH:mm-HH:mm)
     */
    @TableField(value = "loan_dark_hours")
    private String loanDarkHours;

    /**
     * 还款黑暗期 (格式 HH:mm-HH:mm)
     */
    @TableField(value = "repay_dark_hours")
    private String repayDarkHours;

    @ApiModelProperty(value = "资金方授信黑暗期")
    private String fundingCreditDarkHours;

    /**
     * 资金方用信黑暗期
     */
    @TableField(value = "funding_loan_dark_hours")
    private String fundingLoanDarkHours;

    /**
     * 资金方还款黑暗期
     */
    @TableField(value = "funding_repay_dark_hours")
    private String fundingRepayDarkHours;

    /**
     * 日授信限额(万元)
     */
    @TableField(value = "daily_credit_limit")
    private BigDecimal dailyCreditLimit;

    /**
     * 日放款限额(万元)
     */
    @TableField(value = "daily_loan_limit")
    private BigDecimal dailyLoanLimit;

    /**
     * 授信锁定期限(天)
     */
    @TableField(value = "credit_lock_days")
    private Integer creditLockDays;

    /**
     * 用信锁定期限(天)
     */
    @TableField(value = "loan_lock_days")
    private Integer loanLockDays;

    /**
     * 对客利率(%)
     */
    @TableField(value = "customer_interest_rate")
    private String customerInterestRate;

    /**
     * 对资利率(%)
     */
    @TableField(value = "funding_interest_rate")
    private String fundingInterestRate;

    /**
     * 年龄范围(岁) (格式 如 22-55)
     */
    @TableField(value = "age_range")
    private String ageRange;

    /**
     * 支持的还款类型 (英文逗号分隔)
     */
    @TableField(value = "supported_repay_types")
    private RepaymentType supportedRepayTypes;


    /**
     * 借款期限 (英文逗号分隔)
     */
    @TableField(value = "loan_terms")
    private String loanTerms;

    /**
     * 资方路由
     */
    @TableField(value = "capital_route")
    private CapitalRoute capitalRoute;

    /**
     * 项目时效类型（LONGTIME, TEMPORARY）
     */
    @TableField(value = "project_duration_type")
    private ProjectDurationType projectDurationType;

    /**
     * 临时配置有效期起
     */
    @TableField(value = "temp_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime tempStartTime;

    /**
     * 临时配置有效期止
     */
    @TableField(value = "temp_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime tempEndTime;

    /**
     * 启用状态
     */
    @TableField(value = "enabled")
    private AbleStatus enabled;

    /**
     * 年结是否顺延
     */
    @TableField(value = "grace_next")
    private ActiveInactive graceNext;

    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
}
