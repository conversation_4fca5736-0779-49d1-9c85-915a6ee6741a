package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.enums.FlowChannel;
import com.jinghang.cash.enums.WhetherState;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 权益配置修改
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class RightsBasePackageRequest {
    /**
     * 权益配置id
     */
    private String id;
    /**
     * 用户风险等级
     * 1-100
     * -1     兜底权益除外
     */
    private Integer riskLevel;
    /**
     * 内部代码
     * QH_JW_X1 无需权益
     */
    @NotBlank(message = "内部代码不能为空")
    private String code;
    /**
     * 权益类基础包id
     */
    @NotBlank(message = "权益类型包id不能为空")
    private String rightsPackageId;

    /**
     * 权益价格
     */
    @NotNull(message = "售价不能为空")
    private BigDecimal sellingPrice;

    /**
     * 是否强制购买
     * Y-是
     * N-否
     *
     * @see WhetherState
     */
    @NotBlank(message = "是否强制购买不能为空")
    private String approveRightsForce;

    /**
     * 启用/禁用
     * ENABLE -启用
     * DISABLE - 禁用
     *
     * @see AbleStatus
     */
    @NotBlank(message = "启用禁用不能为空")
    private String useStatus;

    /**
     * 流量渠道，兜底权益不绑定，其余不能为空
     *
     * @see FlowChannel
     */
    private String flowChannel;
}
