package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 协议模板配置控制器
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 10:10
 */
@RestController
@RequestMapping("/projectAgreement")
public class ProjectAgreementController {

    @Autowired
    private ProjectAgreementService projectAgreementService;

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode          项目编码
     * @param flowLoanStage        资产方合同签署阶段
     * @param capitalLoanStage     资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @PostMapping("/queryByStageAndType")
    @AnonymousAccess
    public ProjectAgreement getByStageAndType(
            @RequestParam("projectCode") String projectCode,
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage,
            @RequestParam("contractTemplateType") String contractTemplateType) {
        return projectAgreementService.getByStageAndType(projectCode, flowLoanStage, capitalLoanStage, contractTemplateType);
    }

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes      项目编码列表
     * @param isReturnToFlow    是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @PostMapping("/queryByReturnStatus")
    @AnonymousAccess
    public List<ProjectAgreement> getByReturnStatus(
            @RequestParam("projectCodes") List<String> projectCodes,
            @RequestParam(value = "isReturnToFlow", required = false) ActiveInactive isReturnToFlow,
            @RequestParam(value = "isReturnToCapital", required = false) ActiveInactive isReturnToCapital) {
        return projectAgreementService.getByReturnStatus(projectCodes, isReturnToFlow, isReturnToCapital);
    }

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode      项目编码
     * @param flowLoanStage    资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @PostMapping("/queryByStage")
    @AnonymousAccess
    public List<ProjectAgreement> getByStage(
            @RequestParam("projectCode") String projectCode,
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage) {
        return projectAgreementService.getByStage(projectCode, flowLoanStage, capitalLoanStage);
    }
}
