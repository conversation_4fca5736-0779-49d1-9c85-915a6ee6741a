/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @description /
* <AUTHOR>
* @date 2025-08-21
**/
@Data
@TableName("project_contract")
public class ProjectContract implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "项目唯一编码")
    private String projectCode;

    @NotBlank
    @ApiModelProperty(value = "合同模板唯一编码")
    private String contractCode;

    @NotBlank
    @ApiModelProperty(value = "合同英文简称(文件名)")
    private String contractFileName;

    @NotBlank
    @ApiModelProperty(value = "合同模板中文描述")
    private String contractDescription;

    @NotBlank
    @ApiModelProperty(value = "合同归属方  FLOW-资产; BANK-资金 GUARANTEE-融担")
    private String contractOwner;

    @ApiModelProperty(value = "甲方")
    private String partyA;

    @ApiModelProperty(value = "乙方")
    private String partyB;

    @ApiModelProperty(value = "合同开始时间")
    private LocalDateTime contractStartTime;

    @ApiModelProperty(value = "合同结束时间")
    private LocalDateTime contractEndTime;

    @NotBlank
    @ApiModelProperty(value = "启用状态 INIT未生效 Z生效 3 失效")
    private String enabled;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @NotBlank
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @NotBlank
    @ApiModelProperty(value = "文件扩展名")
    private String extension;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    public void copy(ProjectContract source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
