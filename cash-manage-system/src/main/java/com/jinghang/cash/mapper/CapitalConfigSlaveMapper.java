package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.pojo.CapitalConfigSlave;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资金配置
 *
 * <AUTHOR>
 */
@Mapper
@DS("slave")
public interface CapitalConfigSlaveMapper extends BaseMapper<CapitalConfigSlave> {

    List<CapitalConfigSlave> queryAll();

    List<CapitalConfigSlave> getCapitalList();
}




