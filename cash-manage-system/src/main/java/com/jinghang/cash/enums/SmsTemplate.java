package com.jinghang.cash.enums;

import com.jinghang.cash.api.enums.AbleStatus;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
public enum SmsTemplate {

    /**
     * 贷前短信
     */
    MESSAGE_ACOUNT("2", "QH006", "贷前", "对公账户短信", AbleStatus.ENABLE),
    MESSAGE_EQUITY("2", "QH009", "贷前", "权益行权链接短信", AbleStatus.ENABLE);


    private String smsType;
    private String templateNo;
    private String templateType;
    private String desc;
    private AbleStatus ableStatus;

    SmsTemplate(String smsType, String templateNo, String templateType, String desc, AbleStatus ableStatus) {
        this.smsType = smsType;
        this.templateNo = templateNo;
        this.templateType = templateType;
        this.desc = desc;
        this.ableStatus = ableStatus;
    }

    public String getSmsType() {
        return smsType;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public String getTemplateType() {
        return templateType;
    }

    public String getDesc() {
        return desc;
    }

    public AbleStatus getAbleStatus() {
        return ableStatus;
    }
}
