package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface LoanRepository extends JpaRepository<Loan, String> {

    // 查询当天的放款状态成功的记录
    List<Loan> findByLoanTimeBetweenAndLoanStateAndFlowChannel(LocalDateTime startOfDay, LocalDateTime endOfDay, ProcessState loanState, FlowChannel flowChannel);

    // 查询当天的放款状态成功的记录 带上资方渠道
    List<Loan> findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(LocalDateTime startOfDay, LocalDateTime endOfDay, ProcessState loanState, FlowChannel flowChannel, BankChannel bankChannel);

    Loan findByCreditId(String creditId);

    Optional<Loan> findByOrderIdAndLoanState(String orderId, ProcessState loanState);

    Loan findTopByUserIdAndLoanStateOrderByCreatedTimeDesc(String userId, ProcessState state);

    Loan findTopByUserIdAndBankChannelAndLoanStateOrderByCreatedTime(String userId,BankChannel bankChannel, ProcessState state);
    List<Loan> findByUserIdOrderByApplyTimeDesc(String userId);

    List<Loan> findByFlowChannelAndLoanStateAndLoanTimeBetween(FlowChannel flowChannel, ProcessState loanState, LocalDateTime beginTime, LocalDateTime endTime);

    Loan findByOrderId(String orderId);

    Loan findByOrderIdAndFlowChannel(String orderId, FlowChannel flowChannel);


    Optional<Loan> findByIdAndFlowChannelAndBankChannel(String loanId, FlowChannel flowChannel, BankChannel bankChannel);

    Optional<Loan> findTopByOrderIdOrderByCreatedTimeDesc(String orderId);
    @Query("select l from Loan l "
        + "where l.userId = ?1 and l.bankChannel = ?2 and l.loanState =?3 and  l.applyTime<?4 order by l.applyTime desc ")
    List<Loan> findByFile(String userId,BankChannel bankChannel, ProcessState state,LocalDateTime applyTime );

    @Query("select l from Loan l left join Order o on l.orderId = o.id "
        + "where l.loanState = 'SUCCEED' and l.rightsDeductState = 'N' and l.loanTime < ?1 and l.approveRights != 'NONE' and o.orderState = 'LOAN_PASS'")
    List<Loan> findUnPaidRights(LocalDateTime date);

    @Query("select l from Loan l where l.loanState = 'SUCCEED' and l.rightsDeductState = 'N'"
        + "and l.loanTime >= ?1 and l.loanTime < ?2 and l.approveRights != 'NONE'")
    List<Loan> findUnPaidRightsByLoanTime(LocalDateTime beginTime, LocalDateTime endTime);

    Optional<Loan> findByLoanNo(String loanNo);

    int countByLoanStateAndLoanTimeBetween(ProcessState loanState, LocalDateTime beginTime, LocalDateTime endTime);

    List<Loan> findByLoanStateAndLoanTimeBetweenAndFlowChannel(ProcessState loanState, LocalDateTime beginTime, LocalDateTime endTime, FlowChannel flowChannel);

    List<Loan> findByLoanStateAndFlowChannelAndLoanTimeBetween(ProcessState loanState, FlowChannel flowChannel, LocalDateTime beginTime, LocalDateTime endTime);

    List<Loan> findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(ProcessState loanState, BankChannel bankChannel, LocalDateTime startTime, LocalDateTime endTime, FlowChannel flowChannel);

    List<Loan> findByLoanStateAndOrderIdAndBankChannelAndLoanTimeBetween(ProcessState loanState, String orderId, BankChannel bankChannel,
                                                                         LocalDateTime startTime, LocalDateTime endTime);

    @Query("select l from Loan l where l.id > ?1 and l.loanState = 'SUSPEND' and l.flowChannel=?2 and l.bankChannel= ?3 ")
    List<Loan> findSuspendLoan(String id, Pageable pageable,FlowChannel flowChannel,BankChannel bankChannel);

    @Query("select l.id from Loan l where l.loanState = ?1 and l.flowChannel=?2 and l.bankChannel= ?3 ")
    List<String> findIdsByLoanStateAndFlowChannelAndBankChannel(ProcessState loanState,FlowChannel flowChannel,BankChannel bankChannel);

    @Query("select l.id from Loan l where l.loanState = ?1 and l.projectCode=?2 ")
    List<String> findIdsByLoanStateAndProjectCode(ProcessState loanState,String projectCode);

    @Query("select COALESCE(sum(l.amount), 0) from LoanRecord l where l.bankChannel = :bankChannel  and l.loanState in (:states) "
        + " and l.loanTime >= :beginTime and l.loanTime <= :endTime")
    BigDecimal dayOccupyLoanAmountSucceed(BankChannel bankChannel, List<ProcessState> states, LocalDateTime beginTime, LocalDateTime endTime);

    @Query("select COALESCE(sum(l.amount), 0) from LoanRecord l where l.flowChannel = :flowChannel  and l.loanState in (:states) "
        + " and l.loanTime >= :beginTime and l.loanTime <= :endTime")
    BigDecimal dayOccupyLoanAndFlowChanelAmountSucceed(FlowChannel flowChannel, List<ProcessState> states, LocalDateTime beginTime, LocalDateTime endTime);

    @Query("select COALESCE(sum(l.amount), 0) from LoanRecord l where l.flowChannel = :flowChannel  and l.loanState in (:states) "
        + " and l.applyTime >= :beginTime and l.applyTime <= :endTime")
    BigDecimal dayOccupyLoanAmountAndFlowChanelProcessing(FlowChannel flowChannel, List<ProcessState> states, LocalDateTime beginTime, LocalDateTime endTime);


    @Query("select COALESCE(sum(l.amount), 0) from LoanRecord l where l.bankChannel = :bankChannel  and l.loanState in (:states) "
        + " and l.applyTime >= :beginTime and l.applyTime <= :endTime")
    BigDecimal dayOccupyLoanAmountProcessing(BankChannel bankChannel, List<ProcessState> states, LocalDateTime beginTime, LocalDateTime endTime);

    List<Loan> findByUserIdAndFlowChannelAndLoanState(String userId, FlowChannel flowChannel, ProcessState state);

    boolean existsByUserIdAndFlowChannelAndLoanState(String userId, FlowChannel flowChannel, ProcessState state);

    Loan findByOuterLoanId(String outerLoanId);

    Optional<Loan> findByOuterLoanIdAndFlowChannel(String outerLoanId, FlowChannel flowChannel);

    Optional<Loan> findByOuterLoanIdAndFlowChannelAndApplyChannel(String outerLoanId, FlowChannel flowChannel, String applyChannel);

    Optional<Loan> findByOuterLoanIdAndFlowChannelIn(String outerLoanId, List<FlowChannel> flowChannels);

    Integer countByApplyTimeBetweenAndLoanStateIn(LocalDateTime applyTimeStart, LocalDateTime applyTimeEnd, Collection<ProcessState> loanStates);

    @Query("select count(*) from LoanRecord l "
        + "where l.loanState ='PROCESSING' "
        + "and l.applyTime > curdate() "
        + "and timestampdiff(minute ,l.applyTime, now())>15")
    Integer countLoanTimeout();

    @Query("select count(*) from Loan l where l.loanState ='FAILED' "
        + "and l.applyTime > curdate()")
    Integer countLoanFail();

    @Query("select count(*) from LoanRecord l where l.loanState ='FAILED' "
        + "and l.applyTime > curdate()")
    Integer countLoanRecordFail();

    @Query(value = "select "
        + "o.id as loanNo,"
        + "l.periods  as apprvTnr,"
        + "zcar.platform_apply_code as cooppfApplCde,"
        + "if(l.apply_time is null,'',date_format(l.apply_time ,'%Y-%m-%d %H:%i:%s')) as applyDt,"
        + "l.amount as realDownAmt,"
        + "if(l.loan_state= 'SUCCEED','01','02') as dnSts,"
        + "'' as rejectRsn,"
        + "'' as tradeCode,"
        + "if(l.fail_reason is null or l.fail_reason='','',l.fail_reason)rejectReson,"
        + "if(l.loan_time is null,'',date_format(l.loan_time,'%Y-%m-%d')) as loanActvDt,"
        + "l.loan_purpose as purpose,"
        + "day(l.loan_time) as dueOpt,"
        + "rp.plan_repay_date as endDt,"
        + "if(l.bank_rate is null,'',l.bank_rate) as priceIntRat,"
        + "if(l.loan_time is null,'',date_format(l.loan_time,'%Y-%m-%d %H:%i:%s')) as loanActvTime,"
        + "'SYS002' as mtdCde,"
        + "zcar.apply_seq as ysxApplSeq "
        + "from `order` o join loan l "
        + "on o.id = l.order_id "
        + "join repay_plan rp on rp.period = l.periods and rp.loan_id = l.id "
        + "join zycfc_credit_apply_record zcar on zcar.risk_id = o.risk_id "
        + "where o.call_zy_risk =:whetherState and l.apply_time >= :localDateTime  "
        + "and l.apply_time < :localDateTime1 and l.flow_channel ='ZYCFC' and l.loan_state in ('SUCCEED','FAILED')"
        + " union all "
        + "select "
        + "o.id as loanNo,"
        + "l.periods  as apprvTnr,"
        + "zcar.platform_apply_code as cooppfApplCde,"
        + "if(l.apply_time is null,'',date_format(l.apply_time ,'%Y-%m-%d %H:%i:%s')) as applyDt,"
        + "l.amount as realDownAmt,"
        + "if(l.loan_state= 'SUCCEED','01','02') as dnSts,"
        + "'' as rejectRsn,"
        + "'' as tradeCode,"
        + "if(l.fail_reason is null or l.fail_reason='','',l.fail_reason)rejectReson,"
        + "if(l.loan_time is null,'',date_format(l.loan_time,'%Y-%m-%d')) as loanActvDt,"
        + "l.loan_purpose as purpose,"
        + "day(l.loan_time) as dueOpt,"
        + "rp.plan_repay_date as endDt,"
        + "if(l.bank_rate is null,'',l.bank_rate) as priceIntRat,"
        + "if(l.loan_time is null,'',date_format(l.loan_time,'%Y-%m-%d %H:%i:%s')) as loanActvTime,"
        + "'SYS002' as mtdCde,"
        + "zcar.apply_seq as ysxApplSeq "
        + "from `order` o join loan l "
        + "on o.id = l.order_id "
        + "join repay_plan rp on rp.period = l.periods and rp.loan_id = l.id "
        + "join zycfc_credit_apply_record zcar on o.risk_id = zcar.risk_id "
        + "where o.call_zy_risk =:whetherState and  l.flow_channel ='ZYCFC' and l.loan_state in ('SUCCEED','FAILED') "
        + "and l.apply_time >=:localDateTime2 and l.apply_time < :localDateTime and l.updated_time >= :localDateTime "
        + "and l.updated_time  < :localDateTime1 ", nativeQuery = true)
    List<Map<String, String>> getUploadLoanFile(String whetherState, LocalDateTime localDateTime, LocalDateTime localDateTime1, LocalDateTime localDateTime2);

    @Query("select l from Loan l where l.userId = ?1 and l.loanState = ?2")
    List<Loan> findByUserIdAndLoanState(String userId, ProcessState succeed);

    @Query("select l from Loan l "
        + " left join Order o on l.orderId = o.id "
        + " where o.orderState = 'CLEAR' and o.updatedTime >= ?1 and o.updatedTime < ?2 and l.flowChannel = ?3")
    List<Loan> findClearLoanByUpdateTimeBetweenAndFlowChannel(LocalDateTime startTime, LocalDateTime endTime, FlowChannel flowChannel);

    @Query("select l from Loan l "
        + " left join Order o on l.orderId = o.id "
        + " where l.id in ?1 and o.orderState='CLEAR' and l.flowChannel = ?2")
    List<Loan> findClearLoanByLoanIdInAndFlowChannel(List<String> loanIds, FlowChannel flowChannel);


    List<Loan> findByLoanTimeGreaterThanEqualAndLoanTimeLessThanAndFlowChannelAndLoanState(
        LocalDateTime startOfDay, LocalDateTime startOfNextDay, FlowChannel flowChannel, ProcessState processState);

    List<Loan> findAllByUserIdAndLoanState(String id, ProcessState processState);

    List<Loan> findAllByOrderIdIn(List<String> orderIds);

    List<Loan> findAllByUserId(String userId);

    @Query(value = "SELECT " +
        "user_id as userId, " +
        "SUM(CASE WHEN apply_channel = '01' THEN 1 ELSE 0 END) AS LVXIN, " +
        "SUM(CASE WHEN apply_channel = 'p002' THEN 1 ELSE 0 END) AS CJCYDL_PPD2, " +
        "SUM(CASE WHEN apply_channel = 'p001' THEN 1 ELSE 0 END) AS CJCYDL_PPD3 " +
        "FROM loan " +
        "WHERE user_id = :userId " +
        "GROUP BY user_id", nativeQuery = true)
    Map<String, Object> countUserIdLoansByChannel(@Param("userId") String userId);

    List<Loan> findByApplyTimeBetweenAndLoanStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);




    @Query("SELECT SUM(l.amount) FROM Loan l WHERE l.flowChannel = :flowChannel AND l.guaranteeCompany = :guaranteeComp AND l.bankChannel = :bankChannel AND l.loanState IN :loanState AND l.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByDimensionsAndStates(
        @Param("flowChannel") FlowChannel flowChannel,
        @Param("guaranteeComp") GuaranteeCompany guaranteeComp,
        @Param("bankChannel") BankChannel bankChannel,
        @Param("loanState") ProcessState loanState,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    @Query("SELECT SUM(l.amount) FROM Loan l WHERE l.projectCode = :projectCode AND l.loanState IN :loanState AND l.createdTime BETWEEN :startTime AND :endTime")
    Optional<BigDecimal> sumAmountByDimensionsAndStates(
        @Param("projectCode") String projectCode,
        @Param("loanState") ProcessState loanState,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    Loan findTopByUserIdAndFlowChannelAndLoanStateOrderByApplyTime(String userId,FlowChannel flowChannel, ProcessState processState);

    @Query(value = "select l.* from (\n" +
        "select * from (\n" +
        "  SELECT\n" +
        "        id,\n" +
        "        user_id,\n" +
        "        apply_time,\n" +
        "        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY apply_time ASC) as row_num\n" +
        "    FROM loan\n" +
        "    WHERE bank_channel = 'CYBK' and loan_state = 'SUCCEED')a where a.row_num>1) p left join loan l on l.id=p.id", nativeQuery = true)
    List<Loan>  findByloanid();
}

