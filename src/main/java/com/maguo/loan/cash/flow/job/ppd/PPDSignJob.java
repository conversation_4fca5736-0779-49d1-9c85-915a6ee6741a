package com.maguo.loan.cash.flow.job.ppd;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.agreement.LoanAgreementJob;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @作者 Mr.sandman
 * @时间 2025/06/26 16:51
 */
@Component
@JobHandler(value = "ppdSignJob")
public class PPDSignJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PPDSignJob.class);

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private PpdConfig ppdConfig;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private JHReconService jhReconService;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("ppdSignJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if (Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        jobParam.setStartDate(fileDate);
        //保存数据
        jobParam.setTaskHandler("ppdSignJob");
        jobParam.setTaskDescription("协议文件上传-拍拍");
        try {
            List<Loan> list;
            if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
                logger.info("ppdSignJob loanIds size:{}", jobParam.getLoanIds().size());
                //根据借据id数组查询
                list = loanRepository.findAllById(jobParam.getLoanIds());
            } else if (Objects.nonNull(jobParam.getBankChannel())) {
                if (Objects.isNull(jobParam.getStartDate()) || Objects.isNull(jobParam.getEndDate())) {
                    logger.error("ppdSignJob 指定资方,startDate和endDate必填");
                    return;
                }
                logger.info("ppdSignJob 指定资方:{},开始时间:{},结束时间:{}", jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
                //根据资方、放款时间 查询
                list = loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(
                    ProcessState.SUCCEED, jobParam.getBankChannel(), LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.PPCJDL);
            } else {
                logger.info("ppdSignJob 指定开始时间:{},结束时间:{}", fileDate, endDate);
                list = loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(
                    ProcessState.SUCCEED, LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.PPCJDL);
            }
            if (CollectionUtils.isEmpty(list)) {
                logger.info("ppdSignJob 放款成功的借据为空");
                return;
            }
            //获取所有放款单据对应的project-code
            Set<String> projectCodes = list.stream().map(Loan::getProjectCode).collect(Collectors.toSet());
            //回传流量的协议配置
            List<ProjectAgreementDto> agreementToFlowDtoList = projectAgreementFeign.getByReturnStatus(projectCodes.stream().toList(), ActiveInactive.Y.getCode(), "");
            //归属方属于资方的合同协议列表
            List<ProjectAgreementDto> agreementToCapitalDtoList = agreementToFlowDtoList.stream().filter(dto -> TemplateOwner.CAPITAL.name().equals(dto.getTemplateOwner())).toList();
            //所有文件name和对应的文件合同名称
            Map<String, ProjectAgreementDto> filenameFlowMaps = agreementToFlowDtoList.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
            //需要获取资方合同协议的文件合同名称
            Map<String, ProjectAgreementDto> filenameCapitalMaps = agreementToCapitalDtoList.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
            // 开始拉取
            logger.info("借款合同下载,loan size:{}", list.size());
            for (Loan loan : list) {
                Order order = orderRepository.findOrderById(loan.getOrderId());
                logger.info("开始获取协议loanId:{}", loan.getId());
                List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
                Map<String, String> ossMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(userFiles)) {
                    ossMap = userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (k1, k2) -> k2));
                }
                List<FileType> fileTypes = filenameCapitalMaps.keySet().stream().map(filetype -> {
                    String replace = filetype.replace(loan.getProjectCode(), "");
                    return FileType.valueOf(replace);
                }).toList();
                for (FileType fileType : fileTypes) {
                    try {
                        //下载借款合同
                        FileDownloadDto fileDownloadDto = new FileDownloadDto();
                        fileDownloadDto.setLoanId(loan.getLoanNo());
                        fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
                        fileDownloadDto.setType(fileType);
                        fileDownloadDto.setProduct(Product.ZC_CASH);
                        fileDownloadDto.setBankChannel(loan.getBankChannel());
                        //下载资方的合同
                        RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                        logger.info("下载借款合同文件,入参{},出参{}", JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));
                        if (!restResult.isSuccess()) {
                            logger.error("下借款合同文件异常,loanId:{}", loan.getId());
                            continue;
                        }
                        if (Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(),
                            restResult.getData().getOssPath())) {
                            logger.info("合同文件不存在,loanId:{},fileType:{}", loan.getId(), fileType);
                            continue;
                        }
                        // 过滤已经下载的文件
                        if (ossMap.containsKey(restResult.getData().getOssPath()) && ossMap.get(restResult.getData().getOssPath())
                            .equals(restResult.getData().getOssBucket())) {
                            logger.info("借据:{},跳过:{}文件下载", loan.getId(), fileType);
                            continue;
                        }
                        //新增影像协议文件
                        saveUserFile(loan, fileType, restResult);
                    } catch (Exception e) {
                        logger.error("借款合同下载异常loanId:{}", loan.getId(), e);
                    }
                }
                //复借场景需查询客户首借时传输给资金方的协议（综合授权书、数字证书使用授权协议 ），复制一份到复借对应的授信编号的路径下
                loanAgreementJob.handleReloanProtocolFiles(loan);
                // 这里根据riskId查询AgreementSignatureRecord表流量签署的协议
                List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByRiskIdAndSignState(order.getRiskId(), ProcessState.SUCCEED);
                if (CollectionUtil.isNotEmpty(agreementList)) {
                    for (AgreementSignatureRecord record : agreementList) {
                        //获取文件名称规则
                        String fileName = filenameFlowMaps.get(loan.getProjectCode() +
                            record.getFileType().name()).getCapitalContractName();//文件名称
                        //处理流量系统协议文件上传至sftp
                        uploadFlowFileToSftp(loan, record.getCommonOssUrl(), fileName);
                    }
                }

                List<UserFile> userFileList = userFileRepository.findByUserId(loan.getId());
                logger.info("合同文件数量:{}, 内容如下:", userFileList.size());
                if (CollectionUtil.isNotEmpty(userFileList)) {
                    for (UserFile userFile : userFileList) {
                        if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_HEAD) ||
                            userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_NATION) ||
                            userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_FACE)) {
                            continue;
                        }
                        logger.info(" - fileId:{}, fileType:{}, fileName:{}, ossKey:{}",
                            userFile.getId(), userFile.getFileType(), userFile.getFileName(), userFile.getOssKey());
                        //获取资金方合同名称规则
                        String fileName = filenameCapitalMaps.get(loan.getProjectCode() +
                            userFile.getFileType().name()).getCapitalContractName();//文件名称
                        //处理资金系统协议文件上传至sftp
                        uploadCapitalFileToSftp(loan, userFile, fileName);
                    }
                }
            }
            //回调
            jhReconService.saveTaskMonitoringData(jobParam, true, null);
            logger.info("PPDSignJob finished");
        } catch (Exception e) {
            logger.error("拍拍签章上传失败", e);
            e.printStackTrace();
            jhReconService.saveTaskMonitoringData(jobParam, false, e);
        }
    }

    /**
     * 处理流量系统协议文件上传至sftp
     * @param loan 放款记录
     * @param ossKey 公共返回地址
     * @param fileName 文件名称
     * @return 文件上传至sftp路径
     */
    public String uploadFlowFileToSftp(Loan loan, String ossKey, String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(ossBucket, ossKey, inputStream -> {
            try {
                String sftpPath;//文件上传至sftp路径
                //生成包含放款时间、外部借据ID的路径字符串
                String remoteDir = generateLoanTimeFilePath(loan);
                // 根据资方渠道区分不同的sftp账号
                if (Objects.equals(loan.getBankChannel(),BankChannel.CYBK)) {//长银
                    sftpPath = ppdConfig.getSftpDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——长银SFTP, fileName:{}, remotePath:{}", fileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftp(inputStream, fileName, sftpPath);
                } else if (Objects.equals(loan.getBankChannel(),BankChannel.HXBK)) {//湖消
                    sftpPath = ppdConfig.getSftpHxDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——湖消SFTP, fileName:{}, remotePath:{}", fileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, fileName, sftpPath);
                }
                logger.info("协议文件上传拍拍sftp成功");
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }

    /**
     * 处理资金系统协议文件上传至sftp
     * @param loan 放款记录
     * @param userFile 用户文件信息记录
     * @param fileName 文件名称
     * @return 文件上传至sftp路径
     */
    public String uploadCapitalFileToSftp(Loan loan, UserFile userFile, String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                String sftpPath;//文件上传至sftp路径
                //生成包含放款时间、外部借据ID的路径字符串
                String remoteDir = generateLoanTimeFilePath(loan);
                // 根据资方渠道区分不同的sftp账号
                if (Objects.equals(loan.getBankChannel(),BankChannel.CYBK)) {//长银
                    sftpPath = ppdConfig.getSftpDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——长银SFTP, fileName:{}, remotePath:{}", fileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftp(inputStream, fileName, sftpPath);
                } else if (Objects.equals(loan.getBankChannel(),BankChannel.HXBK)) {//湖消
                    sftpPath = ppdConfig.getSftpHxDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——湖消SFTP, fileName:{}, remotePath:{}", fileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, fileName, sftpPath);
                }
                logger.info("协议文件上传拍拍sftp成功");
            } catch (Exception e) {
                logger.error("协议文件上传拍拍sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }

    /**
     * 生成包含放款时间、外部借据ID的路径字符串
     * @param loan 包含放款时间和外部借据ID的对象
     * @return 格式化后的字符串
     */
    private String generateLoanTimeFilePath(Loan loan) {
        if (Objects.isNull(loan) || Objects.isNull(loan.getLoanTime()) || StringUtils.isBlank(loan.getOuterLoanId())) {
            return "";
        }
        String formattedDate = formatLocalDateTime(loan.getLoanTime());
        String outerLoanId = loan.getOuterLoanId();
        logger.info("generateLoanTimeFilePath:{}", formattedDate + "/" + outerLoanId + "/");
        return formattedDate + "/" + outerLoanId + "/";
    }

    /**
     * 将 LocalDateTime 转换为格式为 "yyyyMMdd" 的字符串
     * @param dateTime LocalDateTime 对象
     * @return 格式化后的字符串
     */
    private static String formatLocalDateTime(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }

    private void saveUserFile(Loan loan, FileType fileType, RestResult<FileDownloadResultDto> restResult) {
        FileDownloadResultDto resultData = restResult.getData();
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }
}
