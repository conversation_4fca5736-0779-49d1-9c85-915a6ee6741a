package com.maguo.loan.cash.flow.job.fql;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 借款合同下载
 */
@Component
@JobHandler("fqlLoanAgreementJob")
public class FqlLoanAgreementJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FqlLoanAgreementJob.class);

    private static final String JOB_NAME = "fqlLoanAgreementJob";

    private static final String REPLACE = "#assetId#";

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private FqlConfig fqlConfig;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("{} jobParam: {}", JOB_NAME, JsonUtil.toJsonString(jobParam));

        //job参数校验
        try {
            validateAndProcessJobParam(jobParam);
        } catch (IllegalArgumentException e) {
            logger.error("{} 参数异常: {}", JOB_NAME, e.getMessage());
            return;
        }
        // 拉取所有放款成功的借据
        List<Loan> loans = fetchLoans(jobParam);
        if (CollectionUtils.isEmpty(loans)) {
            logger.info("{} 放款成功的借据为空", JOB_NAME);
            return;
        }
        LocalDate fileDate = jobParam.getStartDate();
        String bizDateStr = fileDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        //获取所有放款单据对应的project-code
        Set<String> projectCodes = loans.stream().map(Loan::getProjectCode).collect(Collectors.toSet());
        //回传流量的协议配置
        List<ProjectAgreementDto> agreementToFlowDtos = projectAgreementFeign.getByReturnStatus(projectCodes.stream().toList(), ActiveInactive.Y.getCode(), "");
        //归属方属于资方的合同协议列表
        List<ProjectAgreementDto> agreementToCapitalDtos = agreementToFlowDtos.stream().filter(dto -> TemplateOwner.CAPITAL.name().equals(dto.getTemplateOwner())).toList();
        //所有文件name和对应的文件合同名称
        Map<String, ProjectAgreementDto> filenameFlowMaps = agreementToFlowDtos.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
        //需要获取资方合同协议的文件合同名称
        Map<String, ProjectAgreementDto> filenameCapitalMaps = agreementToCapitalDtos.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
        //开始拉取
        logger.info("借款合同下载,loan size:{}", loans.size());
        loans.forEach(loan -> processSingleLoan(loan, bizDateStr, filenameFlowMaps, filenameCapitalMaps));
        logger.info("{} finished", JOB_NAME);
    }

    private void validateAndProcessJobParam(JobParam jobParam) {
        if (jobParam.getBankChannel() != null && (jobParam.getStartDate() == null || jobParam.getEndDate() == null)) {
            throw new IllegalArgumentException("指定资方时，开始和结束时间必填");
        }
        if (jobParam.getStartDate() == null) {
            jobParam.setStartDate(LocalDate.now().minusDays(1));
        }
        if (jobParam.getEndDate() == null) {
            jobParam.setEndDate(jobParam.getStartDate());
        }
    }

    private List<Loan> fetchLoans(JobParam jobParam) {
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("{} loanIds size: {}", JOB_NAME, jobParam.getLoanIds().size());
            return loanRepository.findAllById(jobParam.getLoanIds());
        }

        LocalDateTime startDateTime = LocalDateTime.of(jobParam.getStartDate(), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(jobParam.getEndDate(), LocalTime.MAX);

        if (jobParam.getBankChannel() != null) {
            logger.info("{} 指定资方: {}, 开始时间: {}, 结束时间: {}", JOB_NAME, jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(ProcessState.SUCCEED, jobParam.getBankChannel(), startDateTime, endDateTime, FlowChannel.FQLQY001);
        } else {
            logger.info("{} 指定开始时间: {}, 结束时间: {}", JOB_NAME, jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(ProcessState.SUCCEED, startDateTime, endDateTime, FlowChannel.FQLQY001);
        }
    }


    private void processSingleLoan(Loan loan, String bizDateStr, Map<String, ProjectAgreementDto> filenameFlowMaps, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        logger.info("开始处理协议 loanId:{}", loan.getId());

        try {
            // 处理资方合同下载
            processCapitalAgreements(loan, filenameCapitalMaps);

            // 处理流量协议上传
            processFlowAgreements(loan, bizDateStr, filenameFlowMaps);

            // 处理资金系统协议上传
            processCapitalSystemAgreements(loan, bizDateStr, filenameCapitalMaps);

        } catch (Exception e) {
            logger.error("处理贷款协议时发生异常 loanId:{}", loan.getId(), e);
        }
    }

    private void processCapitalAgreements(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        List<FileType> fileTypes = getCapitalFileTypes(loan, filenameCapitalMaps);
        if (CollectionUtils.isEmpty(fileTypes)) {
            return;
        }
        Map<String, String> existingOssMap = getExistingOssFiles(loan);
        fileTypes.forEach(fileType -> {
            try {
                Optional<FileDownloadResultDto> downloadResult = downloadAgreementFile(loan, fileType);
                downloadResult.ifPresent(result -> {
                    if (isFileAlreadyDownloaded(result, existingOssMap)) {
                        logger.info("借据:{}, 跳过已下载文件:{}", loan.getId(), fileType);
                        return;
                    }
                    saveUserFile(loan, fileType, result);
                });
            } catch (Exception e) {
                logger.error("下载资方合同异常 loanId:{}, fileType:{}", loan.getId(), fileType, e);
            }
        });
    }

    private List<FileType> getCapitalFileTypes(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        return filenameCapitalMaps.keySet().stream().filter(key -> key.startsWith(loan.getProjectCode())).map(key -> FileType.valueOf(key.replace(loan.getProjectCode(), ""))).collect(Collectors.toList());
    }

    private Map<String, String> getExistingOssFiles(Loan loan) {
        List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));

        return CollectionUtil.isEmpty(userFiles) ? Collections.emptyMap() : userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (k1, k2) -> k2));
    }

    private Optional<FileDownloadResultDto> downloadAgreementFile(Loan loan, FileType fileType) {
        FileDownloadDto fileDownloadDto = new FileDownloadDto();
        fileDownloadDto.setLoanId(loan.getLoanNo());
        fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        fileDownloadDto.setType(fileType);
        fileDownloadDto.setProduct(Product.ZC_CASH);
        fileDownloadDto.setBankChannel(loan.getBankChannel());

        RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
        logger.info("下载合同文件结果 loanId:{}, fileType:{}, result:{}", loan.getId(), fileType, JSON.toJSONString(restResult));


        if (!restResult.isSuccess() || Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(), restResult.getData().getOssPath())) {
            logger.info("合同文件不存在或下载失败 loanId:{}, fileType:{}", loan.getId(), fileType);
            return Optional.empty();
        }

        return Optional.of(restResult.getData());
    }

    private boolean isFileAlreadyDownloaded(FileDownloadResultDto result, Map<String, String> existingOssMap) {
        return existingOssMap.containsKey(result.getOssPath()) && existingOssMap.get(result.getOssPath()).equals(result.getOssBucket());
    }

    private void processFlowAgreements(Loan loan, String bizDateStr, Map<String, ProjectAgreementDto> filenameFlowMaps) {
        Order order = orderRepository.findOrderById(loan.getOrderId());
        List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByRiskIdAndSignState(order.getRiskId(), ProcessState.SUCCEED);

        if (CollectionUtils.isEmpty(agreementList)) {
            return;
        }

        agreementList.forEach(record -> {
            try {
                String filename = filenameFlowMaps.get(loan.getProjectCode() + record.getFileType().name()).getFlowContractName().replace(REPLACE, order.getOuterOrderId());

                uploadAgreementToSftp(record.getCommonOssUrl(), filename, bizDateStr);
            } catch (Exception e) {
                logger.error("上传流量协议到SFTP失败 loanId:{}, recordId:{}", loan.getId(), record.getId(), e);
            }
        });
    }

    private void processCapitalSystemAgreements(Loan loan, String bizDateStr, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        Order order = orderRepository.findOrderById(loan.getOrderId());
        List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(order.getUserId(), loan.getId());

        if (CollectionUtils.isEmpty(userFileList)) {
            return;
        }

        userFileList.stream().filter(file -> !FileType.CREDIT_SETTLE_VOUCHER_FILE.name().equals(file.getFileType().name())).forEach(userFile -> {
            try {
                String filename = filenameCapitalMaps.get(loan.getProjectCode() + userFile.getFileType().name()).getCapitalContractName().replace(REPLACE, order.getOuterOrderId());

                uploadAgreementToSftp(userFile.getOssKey(), filename, bizDateStr);
            } catch (Exception e) {
                logger.error("上传资方协议到SFTP失败 loanId:{}, fileId:{}", loan.getId(), userFile.getId(), e);
            }
        });
    }

    private void uploadAgreementToSftp(String ossPath, String filename, String bizDateStr) {
        fileService.getOssFile(ossBucket, ossPath, inputStream -> {
            try {
                sftpUtils.fqlUploadStreamToSftp(inputStream, filename, fqlConfig.getAgreementSftpPath(bizDateStr));
            } catch (Exception e) {
                logger.error("协议文件上传SFTP失败 filename:{}", filename, e);
                throw new RuntimeException(e);
            }
        });
    }

    private void saveUserFile(Loan loan, FileType fileType, FileDownloadResultDto resultData) {
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName()) ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT, PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }

    public String uploadSignFileToSftp(Loan loan, Order order, UserFile userFile, String filename, String bizDateStr) {
        AtomicReference<String> result = new AtomicReference<>("");
        String newFilename = filename.replace(REPLACE, order.getOuterOrderId());
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                result.set(fqlConfig.getAgreementSftpPath(bizDateStr));
                sftpUtils.fqlUploadStreamToSftp(inputStream, newFilename, fqlConfig.getAgreementSftpPath(bizDateStr));
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }
}
