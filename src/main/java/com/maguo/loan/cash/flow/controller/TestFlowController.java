package com.maguo.loan.cash.flow.controller;

import com.maguo.loan.cash.flow.entrance.common.dto.request.DbCrudDTO;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "test/")
public class TestFlowController {

    @Autowired
    private TestService testService;

    @RequestMapping(value = "flow")
    public @ResponseBody LvxinResponse testFlow(@RequestBody ApprovalRequest request) {
        return LvxinResponse.success();
    }

    @RequestMapping(value = "crud")
    public String testCrud(@RequestBody DbCrudDTO request) {
        return testService.test(request.getBizId());
    }
}
