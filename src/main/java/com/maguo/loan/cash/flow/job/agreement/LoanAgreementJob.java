package com.maguo.loan.cash.flow.job.agreement;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 借款合同下载
 */
@Component
@JobHandler("loanAgreementJob")
public class LoanAgreementJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanAgreementJob.class);

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private LvXinConfig lvXinConfig;

    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Autowired
    private JHReconService jhReconService;
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("loanAgreementJob jobParam:{}", JsonUtil.toJsonString(jobParam));

        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if (Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        jobParam.setTaskHandler("loanAgreementJob");
        jobParam.setTaskDescription("协议文件上传-绿信");
        jobParam.setStartDate(fileDate);
        List<Loan> list;
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("loanAgreementJob loanIds size:{}", jobParam.getLoanIds().size());
            //根据借据id数组查询
            list = loanRepository.findAllById(jobParam.getLoanIds());
        } else if (jobParam.getBankChannel() != null) {
            if (jobParam.getStartDate() == null || jobParam.getEndDate() == null) {
                logger.error("loanAgreementJob 指定资方,startDate和endDate必填");
                return;
            }
            logger.info("loanAgreementJob 指定资方:{},开始时间:{},结束时间:{}", jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            //根据资方、放款时间 查询
            list = loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, jobParam.getBankChannel(), LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.LVXIN);
        } else {
            logger.info("loanAgreementJob 指定开始时间:{},结束时间:{}", fileDate, endDate);
            list = loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.LVXIN);
        }

        if (CollectionUtils.isEmpty(list)) {
            logger.info("loanAgreementJob 放款成功的借据为空");
            return;
        }
        Set<String> projectCodes = list.stream().map(Loan::getProjectCode).collect(Collectors.toSet());
        //回传流量资金方的合同
        List<ProjectAgreementDto> agreementToCapitalDtos = projectAgreementFeign.getByReturnStatus(projectCodes.stream().toList(), "", ActiveInactive.Y.getCode());
        Map<String, ProjectAgreementDto> filenameCapitalMaps = agreementToCapitalDtos.stream()
            .collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode()+agreementDto.getContractTemplateType().name(),
                Function.identity()
            ));

        // 开始拉取
        logger.info("借款合同下载,loan size:{}", list.size());
        for (Loan loan : list) {
            Order order = orderRepository.findOrderById(loan.getOrderId());

            logger.info("开始获取协议loanId:{}", loan.getId());
            List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));

            Map<String, String> ossMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userFiles)) {
                ossMap = userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (k1, k2) -> k2));
            }
            List<FileType> fileTypes = filenameCapitalMaps.keySet().stream().map(filetype -> {
                String replace = filetype.replace(loan.getProjectCode(), "");
                return FileType.valueOf(replace);
            }).toList();

            for (FileType fileType : fileTypes) {
                try {
                    //下载借款合同
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setLoanId(loan.getLoanNo());
                    fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
                    fileDownloadDto.setType(fileType);
                    fileDownloadDto.setProduct(Product.ZC_CASH);
                    fileDownloadDto.setBankChannel(loan.getBankChannel());
                    RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                    logger.info("下载借款合同文件,入参{},出参{}", JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));
                    if (!restResult.isSuccess()) {
                        //warningService.warn("下载借款合同文件异常:" + JSON.toJSONString(fileDownloadDto),
                        //    msg -> logger.error("下借款合同文件异常:{}", JSON.toJSONString(restResult)));
                        logger.error("下借款合同文件异常,loanId:{}", loan.getId());
                        continue;
                    }
                    if (Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(),
                        restResult.getData().getOssPath())) {
                        logger.info("合同文件不存在,loanId:{},fileType:{}", loan.getId(), fileType);
                        continue;
                    }
                    // 过滤已经下载的文件
                    if (ossMap.containsKey(restResult.getData().getOssPath()) && ossMap.get(restResult.getData().getOssPath())
                        .equals(restResult.getData().getOssBucket())) {
                        logger.info("借据:{},跳过:{}文件下载", loan.getId(), fileType);
                        continue;
                    }
                    //新增影像协议文件
                    saveUserFile(loan, fileType, restResult);
                } catch (Exception e) {
                    logger.error("借款合同下载异常loanId:{}", loan.getId(), e);
                }
            }

            // 这里根据riskId查询AgreementSignatureRecord表流量签署的协议
            List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByRiskIdAndSignState(order.getRiskId(), ProcessState.SUCCEED);
            if (agreementList != null && agreementList.size() > 0) {
                for (AgreementSignatureRecord record : agreementList) {
                    String key = loan.getProjectCode() + record.getFileType().name();
                    String fileName = filenameCapitalMaps.get(key).getCapitalContractName();
                    uploadFlowAgreementFileToSftp(loan, record.getCommonOssUrl(), record.getFileType(), order,fileName);
                }
            }
            //复借场景需查询客户首借时传输给资金方的协议（综合授权书、数字证书使用授权协议 ），复制一份到复借对应的授信编号的路径下
            handleReloanProtocolFiles(loan);

            logger.info("开始获取资金系统协议参数loanId:{}  userId:{}", loan.getId(), loan.getUserId());
            // 获取资金系统签署完成与资方返回的协议
            List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(loan.getUserId() ,loan.getId());
            logger.info("获取资金系统签署完成与资方返回的协议:{}", JsonUtil.toJsonString(userFileList));
            if (userFileList != null && userFileList.size() > 0) {
                for (UserFile userFile : userFileList) {
                    if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        continue;
                    }
                    String key = loan.getProjectCode() + userFile.getFileType().name();
                    String fileName = filenameCapitalMaps.get(key).getCapitalContractName();

                    uploadCoreAgreementFileToSftp(loan, userFile, order,fileName);
                }
            }
        }
        //回调
        jhReconService.saveTaskMonitoringData(jobParam,true,null);
        logger.info("loanAgreementJob finished");
    }

    public String uploadCoreAgreementFileToSftp(Loan loan, UserFile userFile, Order order,String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                // 根据资方渠道区分不同的sftp账号
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    //不是权益客户保持之前逻辑
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                        result.set(lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                    } else {
                        result.set(lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    }
                } else if (loan.getBankChannel() == BankChannel.HXBK) {
                    result.set(lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                }
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }

    public String uploadFlowAgreementFileToSftp(Loan loan, String ossKey, com.maguo.loan.cash.flow.enums.FileType fileType,
                                                Order order,String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(ossBucket, ossKey, inputStream -> {
            try {
                // 根据资方渠道区分不同的sftp账号
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    //不是权益客户保持之前逻辑
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                        result.set(lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                    } else {
                        result.set(lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    }
                } else if (loan.getBankChannel() == BankChannel.HXBK) {
                    result.set(lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                }
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }


    /**
     * 处理前一笔场景下的协议文件复制
     * 从上一条记录复制协议文件到当前复借记录，如不存在则从资金方系统获取
     */
    public void handleReloanProtocolFiles(Loan loan) {
        if (!WhetherState.Y.equals(loan.getReloan())) {
            return;
        }

        // 1. 查询用户前一笔成功记录
        List<Loan> originalLoans = loanRepository.findByFile(loan.getUserId(), loan.getBankChannel(), ProcessState.SUCCEED, loan.getApplyTime());

        if (CollectionUtils.isEmpty(originalLoans)) {
            logger.info("未找到用户首借成功记录，userId: {}", loan.getUserId());
            return;
        }
        List<com.maguo.loan.cash.flow.enums.FileType> existingFileTypes = Arrays.asList(
            com.maguo.loan.cash.flow.enums.FileType.SYNTHESIS_AUTHORIZATION,
            com.maguo.loan.cash.flow.enums.FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER
        );

        // 3. 尝试从已有文件中复制
        List<UserFile> files = userFileRepository.findByUserIdAndLoanNoAndFileTypeInOrderByUpdatedTimeDesc(
            loan.getUserId(), loan.getId(), existingFileTypes);
        if (!CollectionUtils.isEmpty(files)&&files.size()==2) {
            logger.info("已经有对应签章文件，userId: {}", loan.getUserId());
            return;
        }
        Loan originalLoan = originalLoans.get(0);
        // 2. 定义需要处理的文件类型
        List<FileType> targetFileTypes = Arrays.asList(
            FileType.SYNTHESIS_AUTHORIZATION,
            FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER
        );


        // 3. 尝试从已有文件中复制
        List<UserFile> existingFiles = userFileRepository.findByUserIdAndLoanNoAndFileTypeInOrderByUpdatedTimeDesc(
            originalLoan.getUserId(), originalLoan.getId(), existingFileTypes);

        if (!CollectionUtils.isEmpty(existingFiles)) {
            copyExistingFiles(loan, existingFiles, targetFileTypes);
        } else {
            // 4. 从资金方系统获取文件
            downloadFilesFromFinancialSystem(loan, originalLoan, targetFileTypes);
        }
    }

    /**
     * 复制已存在的文件到当前贷款记录
     */
    private void copyExistingFiles(Loan currentLoan, List<UserFile> existingFiles, List<FileType> targetFileTypes) {
        for (UserFile file : existingFiles) {
            String fileTypesStr = targetFileTypes.stream()
                .map(FileType::name)
                .collect(Collectors.joining(","));
            if (!fileTypesStr.contains(file.getFileType().name())) {
                continue;
            }
            try {
                FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(file.getFileType());
                FileDownloadResultDto fileDto = new FileDownloadResultDto();
                fileDto.setFileName(file.getFileName());
                fileDto.setOssBucket(file.getOssBucket());
                fileDto.setOssPath(file.getOssKey());
                fileDto.setFileStatus(ProcessStatus.SUCCESS);
                saveUserFile(currentLoan, cashFileType, RestResult.success(fileDto));
                logger.info("成功复制文件到当前贷款记录，loanId: {}, fileType: {}",
                    currentLoan.getId(), file.getFileType());

            } catch (Exception e) {
                logger.error("复制文件失败，loanId: {}, fileId: {}, error: {}",
                    currentLoan.getId(), file.getId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 从资金方系统下载文件
     */
    private void downloadFilesFromFinancialSystem(Loan currentLoan, Loan originalLoan, List<FileType> targetFileTypes) {
        for (FileType fileType : targetFileTypes) {
            try {
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setLoanId(originalLoan.getLoanNo());
                fileDownloadDto.setLoanOrderId(Optional.ofNullable(originalLoan.getLoanRecordId())
                    .orElse(originalLoan.getId()));
                fileDownloadDto.setType(fileType);
                fileDownloadDto.setProduct(Product.ZC_CASH);
                fileDownloadDto.setBankChannel(currentLoan.getBankChannel());

                RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                logger.info("下载借款合同文件，入参: {}, 出参: {}",
                    JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));

                if (!restResult.isSuccess()) {
                    logger.error("下载借款合同文件异常，loanId: {}, fileType: {}",
                        currentLoan.getId(), fileType);
                    continue;
                }

                FileDownloadResultDto resultData = restResult.getData();
                if (Objects.isNull(resultData) ||
                    StringUtils.isAnyBlank(resultData.getOssBucket(), resultData.getOssPath())) {
                    logger.info("合同文件不存在，loanId: {}, fileType: {}",
                        currentLoan.getId(), fileType);
                    continue;
                }
                saveUserFile(currentLoan, fileType, restResult);
                logger.info("成功从资金方系统下载文件，loanId: {}, fileType: {}", currentLoan.getId(), fileType);

            } catch (Exception e) {
                logger.error("从资金方系统下载文件失败，loanId: {}, fileType: {}, error: {}",
                    currentLoan.getId(), fileType, e.getMessage(), e);
            }
        }
    }

    private void saveUserFile(Loan loan, FileType fileType, RestResult<FileDownloadResultDto> restResult) {
        FileDownloadResultDto resultData = restResult.getData();
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                 PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }


    /**
     * 根据借据、订单和文件类型，解析出最终要上传的SFTP文件名。
     * @param loan 借据信息
     * @param fileType 文件类型
     * @param isCapitalOwner 文件是否属于资金方
     */
    public String resolveSftpFileName(Loan loan, String fileType, boolean isCapitalOwner) {
        String projectCode = loan.getProjectCode();
        try {

            ProjectAgreementDto config = projectAgreementFeign.getByStageAndType(projectCode, "", "", fileType);

            if (config == null) {
                logger.warn("未找到文件名配置, projectCode:{}, fileType:{}", projectCode, fileType);
                return null;
            }


            // 根据文件归属方，获取对应的文件名模板
            String fileNameTemplate = isCapitalOwner ? config.getCapitalContractName() : config.getFlowContractName();

            if (StringUtils.isBlank(fileNameTemplate)) {
                logger.warn("文件名模板为空, config:{}", JsonUtil.toJsonString(config));
                return null;
            }

            return fileNameTemplate;

        } catch (Exception e) {
            logger.error("解析SFTP文件名失败, projectCode:{}, fileType:{}", projectCode, fileType, e);
            return null;
        }
    }

}
