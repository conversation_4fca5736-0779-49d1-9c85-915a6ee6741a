<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">资产方编码</label>
        <el-input v-model="query.flowChannel" clearable placeholder="请输入资产方编码" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">资产方公司名称</label>
        <el-input v-model="query.flowName" clearable placeholder="请输入资产方公司名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">资产方状态</label>
        <el-select v-model="query.enabled" clearable size="small" placeholder="请选择资产方状态" class="filter-item" style="width: 90px" @change="crud.toQuery">
          <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name" :value="item.key" />
        </el-select>
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" >
        <el-button slot="right"
          v-if="crud.optShow.add"
          v-permission="permission.dtl"
          class="filter-item"
          size="mini"
          type="info"
          icon="el-icon-document"
          @click="jumpPage"
        >
          查看
        </el-button>
        <el-button slot="right"
          v-if="crud.optShow.add"
          v-permission="permission.dtl"
          class="filter-item"
          size="mini"
          type="danger"
          icon="el-icon-circle-close"
          @click="stopUse"
        >
          停用
        </el-button>
      </crudOperation>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="580px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="资产方编码" prop="flowChannel">
            <el-input v-model="form.flowChannel" style="width: 370px;" clearable placeholder="请输入资产方编码" />
          </el-form-item>
          <el-form-item label="资产方简称" prop="flowNameShort">
            <el-input v-model="form.flowNameShort" style="width: 370px;" clearable placeholder="请输入资产方简称" />
          </el-form-item>
          <el-form-item label="资产方公司名称" prop="flowName">
            <el-input v-model="form.flowName" style="width: 370px;" clearable placeholder="请输入资产方公司名称" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="flowChannel" label="资产方编码" />
        <el-table-column prop="flowNameShort" label="资产方简称" />
        <el-table-column prop="enabled" label="资产方状态">
          <template slot-scope="scope">
            {{ dict.label.ableStatus[scope.row.enabled] }}
          </template>
        </el-table-column>
        <el-table-column prop="flowName" label="资产方公司名称" />
        <el-table-column prop="mainProd" label="主营产品" />
        <el-table-column prop="contactPerson" label="联系人" />
        <el-table-column prop="contactPhone" label="联系方式" />
        <el-table-column prop="flowDesc" label="公司简介" />
        <!-- <el-table-column v-if="checkPer(['admin','flowConfig:edit','flowConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column> -->
      </el-table>
      <!--分页组件-->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    </div>
  </div>
</template>

<script>
import crudAsset from '@/api/projectManage/asset'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, flowChannel: null, enabled: null, remark: null, revision: null, createdBy: null, createdTime: null, updatedBy: null, updatedTime: null, flowNameShort: null, flowName: null, flowDesc: null, contactPerson: null, contactPhone: null, emailAddress: null, mainProd: null }
export default {
  name: 'AssetList',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ableStatus'],
  cruds() {
    return CRUD({ title: '资产方', url: 'api/flowConfig', idField: 'id', sort: 'id,desc', crudMethod: { ...crudAsset }, optShow: {add: true, edit: true, reset: true}})
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        orderState: undefined,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined,
        packageStatus: undefined,
        bankChannel:undefined,
      },
      permission: {
        add: ['admin', 'flowConfig:add'],
        edit: ['admin', 'flowConfig:edit'],
        dtl: ['admin', 'flowConfig:dtl'],
        use: ['admin', 'flowConfig:use']
      },
      rules: {
        flowChannel: [
          { required: true, message: '资产方编码不能为空', trigger: 'blur' }
        ],
        flowNameShort: [
          { required: true, message: '资产方简称不能为空', trigger: 'blur' }
        ],
        flowName: [
          { required: true, message: '资产方公司名称不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'flowChannel', display_name: '资产方编码' },
        { key: 'enabled', display_name: '资产方状态' },
        { key: 'flowName', display_name: '资产方公司名称' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 查看
    jumpPage() {
      this.$router.push({name: 'AssetDtl'})
    }
  }
}
</script>

<style scoped>

</style>
