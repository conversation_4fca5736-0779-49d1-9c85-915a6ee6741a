<template>
    <div class="app-container">
      <el-collapse v-model="activeName">
        <el-collapse-item title="融担方详情" name="1">
          <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="融担方编码" prop="flowChannel">
                  <el-input v-model="form.flowChannel" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="融担方简称" prop="flowNameShort">
                  <el-input v-model="form.flowNameShort" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="融担方公司名称" prop="flowName">
                  <el-input v-model="form.flowName" style="width: 280px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="主营产品" prop="mainProd">
                  <el-input v-model="form.mainProd" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input v-model="form.contactPerson" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系方式" prop="contactPhone">
                  <el-input v-model="form.contactPhone" style="width: 280px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="资产方接入日期" prop="contractCode">
                  <el-date-picker
                    v-model="value2"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    :picker-options="pickerOptions" 
                    style="width: 280px;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="公司简介" prop="flowDesc">
                  <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入公司简介" v-model="form.flowDesc" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="合同列表" name="2">
          <!--工具栏-->
          <div class="head-container">
            <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
            <!-- <crudOperation :permission="permission" /> -->
            <el-button 
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click.native="addContract()"
            >
              上传
            </el-button>
            <el-button 
              class="filter-item"
              size="mini"
              type="warning"
              icon="el-icon-download"
              @click.native="downContract"
            >
              批量下载
            </el-button>
            <!--表单组件-->
            <el-dialog :close-on-click-modal="false" :visible.sync="isOpen" title="新增合同" width="1000px">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同编码" prop="contractCode">
                      <el-input v-model="form.contractCode" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同名称" prop="contractFileName">
                      <el-input v-model="form.contractFileName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="甲方" prop="partyA">
                      <el-input v-model="form.partyA" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="乙方" prop="partyB">
                      <el-input v-model="form.partyB" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="22">
                    <el-form-item label="合同描述" prop="contractDescription">
                      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入合同描述" v-model="form.contractDescription" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="16">
                    <el-form-item label="上传合同" prop="contractDescription">
                      <input type="file" @change="uploadFile" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button type="text" @click.native="cancel">取消</el-button>
                <el-button type="primary" @click.native="submit">确认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="contractCode" label="合同编码" />
              <el-table-column prop="contractFileName" label="合同名称" />
              <el-table-column prop="contractDescription" label="合同描述" />
              <el-table-column prop="partyA" label="甲方" />
              <el-table-column prop="partyB" label="乙方" />
              <!-- <el-table-column v-if="checkPer(['admin','projectContract:edit','projectContract:del'])" label="操作" width="150px" align="center">
                <template slot-scope="scope">
                  <udOperation
                    :data="scope.row"
                    :permission="permission"
                  />
                </template>
              </el-table-column> -->
            </el-table>
            <!--分页组件-->
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
</template>

<script>
import crudContract from '@/api/projectManage/contract'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, projectCode: null, enabled: null, createBy: null, createTime: null, updateBy: null, updateTime: null, contractCode: null, contractFileName: null, contractDescription: null, contractOwner: null, partyA: null, partyB: null, contractStartTime: null, contractEndTime: null, fileType: null, extension: null, ext1: null, ext2: null }
export default {
  name: 'AssetDtl',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'mt', url: 'api/projectContract', idField: 'id', sort: 'id,desc', crudMethod: { ...crudContract }})
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        orderState: undefined,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined,
        packageStatus: undefined,
        bankChannel:undefined,
      },
      activeNames: ['1', '2'],
      isOpen: false,
      fileList: [],
      permission: {
        add: ['admin', 'projectContract:add'],
        edit: ['admin', 'projectContract:edit'],
        del: ['admin', 'projectContract:del']
      },
      rules: {
        flowChannel: [
          { required: true, message: '资产方编码不能为空', trigger: 'blur' }
        ],
        flowNameShort: [
          { required: true, message: '资产方简称不能为空', trigger: 'blur' }
        ],
        flowName: [
          { required: true, message: '资产方公司名称不能为空', trigger: 'blur' }
        ],
        mainProd: [
          { required: true, message: '主营产品不能为空', trigger: 'blur' }
        ],
        contractCode: [
          { required: true, message: '资产方接入日期不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    addContract() {
      this.isOpen = true;
    },

    downContract() {
      
    },

    cancel() {
      this.isOpen = false;
    },

    submit(event) {
      this.isOpen = false;
      const file = event.target.files[0];
      formData.append('file', file);
      
    }
  }
}
</script>

<style scoped>
::v-deep .el-collapse-item__header{
  font-size: 18px;
  font-weight: 600;
}
</style>
