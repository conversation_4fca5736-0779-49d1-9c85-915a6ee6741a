<template>
    <div class="app-container">
      <el-collapse :value ="activeNames">
        <el-collapse-item title="项目详情" name="1">
          <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="项目编码" prop="flowChannel">
                  <el-input v-model="form.flowChannel" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目名称" prop="flowNameShort">
                  <el-input v-model="form.flowNameShort" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目状态" prop="flowName">
                  <el-input v-model="form.flowName" style="width: 280px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="渠道方" prop="mainProd">
                  <el-input v-model="form.mainProd" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="融担方" prop="contactPerson">
                  <el-input v-model="form.contactPerson" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资金方" prop="contactPhone">
                  <el-input v-model="form.contactPhone" style="width: 280px;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="产品类型" prop="contactPhone">
                  <el-input v-model="form.contactPhone" style="width: 280px;" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目开始日期" prop="contractCode">
                  <el-date-picker
                    v-model="value2"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    :picker-options="pickerOptions" 
                    style="width: 280px;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目结束日期" prop="contractCode">
                  <el-date-picker
                    v-model="value2"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    :picker-options="pickerOptions" 
                    style="width: 280px;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <el-tabs v-model="activeName">
        <el-tab-pane label="产品要素" name="first">
          <el-collapse :value="activeNames">
            <el-collapse-item title="基本要素" name="2">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="项目编码" prop="flowChannel">
                      <el-input v-model="form.flowChannel" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目名称" prop="flowNameShort">
                      <el-input v-model="form.flowNameShort" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目状态" prop="flowName">
                      <el-input v-model="form.flowName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="渠道方" prop="mainProd">
                      <el-input v-model="form.mainProd" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="融担方" prop="contactPerson">
                      <el-input v-model="form.contactPerson" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="资金方" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品类型" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="授信" name="3">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="项目编码" prop="flowChannel">
                      <el-input v-model="form.flowChannel" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目名称" prop="flowNameShort">
                      <el-input v-model="form.flowNameShort" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目状态" prop="flowName">
                      <el-input v-model="form.flowName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="渠道方" prop="mainProd">
                      <el-input v-model="form.mainProd" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="融担方" prop="contactPerson">
                      <el-input v-model="form.contactPerson" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="资金方" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品类型" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="放款" name="4">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="项目编码" prop="flowChannel">
                      <el-input v-model="form.flowChannel" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目名称" prop="flowNameShort">
                      <el-input v-model="form.flowNameShort" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目状态" prop="flowName">
                      <el-input v-model="form.flowName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="渠道方" prop="mainProd">
                      <el-input v-model="form.mainProd" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="融担方" prop="contactPerson">
                      <el-input v-model="form.contactPerson" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="资金方" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品类型" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="还款" name="5">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="项目编码" prop="flowChannel">
                      <el-input v-model="form.flowChannel" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目名称" prop="flowNameShort">
                      <el-input v-model="form.flowNameShort" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目状态" prop="flowName">
                      <el-input v-model="form.flowName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="渠道方" prop="mainProd">
                      <el-input v-model="form.mainProd" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="融担方" prop="contactPerson">
                      <el-input v-model="form.contactPerson" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="资金方" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品类型" prop="contactPhone">
                      <el-input v-model="form.contactPhone" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="项目结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div style="text-align: center; margin-top: 30px;">
            <el-button round @click="cancel">取消</el-button>
            <el-button type="primary" round @click="submit">保存</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="风控信息" name="second">
          
        </el-tab-pane>
        <el-tab-pane label="临时配置项" name="third">
          <!--工具栏-->
          <div class="head-container">
            <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
            <crudOperation :permission="permission" >
              <el-button slot="right"
                v-if="crud.optShow.add"
                v-permission="permission.dtl"
                class="filter-item"
                size="mini"
                type="info"
                icon="el-icon-document"
                @click.native="jumpPage"
              >
                查看
              </el-button>
              <el-button slot="right"
                v-if="crud.optShow.add"
                v-permission="permission.dtl"
                class="filter-item"
                size="mini"
                type="danger"
                icon="el-icon-circle-close"
                @click.native="stopUse"
              >
                停用
              </el-button>
            </crudOperation>
            <!--表单组件-->
            <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1000px">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同编码" prop="contractCode">
                      <el-input v-model="form.contractCode" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同名称" prop="contractFileName">
                      <el-input v-model="form.contractFileName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="甲方" prop="partyA">
                      <el-input v-model="form.partyA" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="乙方" prop="partyB">
                      <el-input v-model="form.partyB" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同开始日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同结束日期" prop="contractCode">
                      <el-date-picker
                        v-model="value2"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        :picker-options="pickerOptions" 
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="22">
                    <el-form-item label="合同描述" prop="contractDescription">
                      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入合同描述" v-model="form.contractDescription" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button type="text" @click="crud.cancelCU">取消</el-button>
                <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="contractCode" label="合同编码" />
              <el-table-column prop="contractFileName" label="合同名称" />
              <el-table-column prop="contractDescription" label="合同描述" />
              <el-table-column prop="partyA" label="甲方" />
              <el-table-column prop="partyB" label="乙方" />
              <!-- <el-table-column v-if="checkPer(['admin','projectContract:edit','projectContract:del'])" label="操作" width="150px" align="center">
                <template slot-scope="scope">
                  <udOperation
                    :data="scope.row"
                    :permission="permission"
                  />
                </template>
              </el-table-column> -->
            </el-table>
            <!--分页组件-->
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="其他配置" name="fourth">
          
        </el-tab-pane>
      </el-tabs>
    </div>
</template>

<script>
import crudProject from '@/api/projectManage/project'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, projectCode: null, enabled: null, createBy: null, createTime: null, updateBy: null, updateTime: null, contractCode: null, contractFileName: null, contractDescription: null, contractOwner: null, partyA: null, partyB: null, contractStartTime: null, contractEndTime: null, fileType: null, extension: null, ext1: null, ext2: null }
export default {
  name: 'ProjectDtl',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '临时配置', url: 'api/projectInfo', idField: 'id', sort: 'id,desc', crudMethod: { ...crudProject }, optShow: {add: true, edit: true}})
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        orderState: undefined,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined,
        packageStatus: undefined,
        bankChannel:undefined,
      },
      activeNames: ['1', '2', '3', '4', '5', '6'],
      activeName: 'first',
      permission: {
        add: ['admin', 'projectContract:add'],
        edit: ['admin', 'projectContract:edit'],
        del: ['admin', 'projectContract:del']
      },
      rules: {
        flowChannel: [
          { required: true, message: '资产方编码不能为空', trigger: 'blur' }
        ],
        flowNameShort: [
          { required: true, message: '资产方简称不能为空', trigger: 'blur' }
        ],
        flowName: [
          { required: true, message: '资产方公司名称不能为空', trigger: 'blur' }
        ],
        mainProd: [
          { required: true, message: '主营产品不能为空', trigger: 'blur' }
        ],
        contractCode: [
          { required: true, message: '资产方接入日期不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }

  }
}
</script>

<style scoped>
::v-deep .el-collapse-item__header{
  font-size: 18px;
  font-weight: 600;
}
</style>
