# 拍拍到湖消路由分析及绿信改造方案

## 📋 当前拍拍到湖消的路由机制

### 1. 路由架构概述

系统通过三层配置实现渠道到资方的路由：

```
申请渠道(ApplyChannel) → 流量渠道(FlowChannel) → 资方渠道(BankChannel)
```

### 2. 拍拍贷当前配置

#### 申请渠道配置 (ApplyChannel)
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/ApplyChannel.java" mode="EXCERPT">
````java
public enum ApplyChannel {
    CJCYDL_PPD2("p002","拍拍贷-低定价客群"),
    CJCYDL_PPD3("p001","拍拍贷-高定价客群")
}
````
</augment_code_snippet>

#### 流量渠道配置 (FlowChannel)
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/FlowChannel.java" mode="EXCERPT">
````java
public enum FlowChannel {
    PPCJDL("信也--拍拍", FlowType.IRR36, "拍拍贷"),
}
````
</augment_code_snippet>

#### 资方渠道配置 (BankChannel)
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/enums/BankChannel.java" mode="EXCERPT">
````java
public enum BankChannel {
    HXBK("HXBK", "湖消 间接连接，通过对接蚂蚁放款", new BigDecimal("0.2399")),
}
````
</augment_code_snippet>

### 3. 数据库路由配置

#### 核心路由表结构
```sql
-- flow_config (流量配置表)
CREATE TABLE flow_config (
    id VARCHAR PRIMARY KEY,
    flow_channel VARCHAR,           -- 流量渠道 (如PPCJDL)
    enabled VARCHAR,                -- 启用状态
    -- 其他字段...
);

-- capital_config (资方配置表)  
CREATE TABLE capital_config (
    id VARCHAR PRIMARY KEY,
    bank_channel VARCHAR,           -- 资方渠道 (如HXBK)
    enabled VARCHAR,                -- 启用状态
    -- 其他字段...
);

-- flow_route_config (流量路由配置表) - 核心路由表
CREATE TABLE flow_route_config (
    id VARCHAR PRIMARY KEY,
    flow_id VARCHAR,                -- 流量ID (关联flow_config.id)
    capital_id VARCHAR,             -- 资金ID (关联capital_config.id)
    priority INTEGER,               -- 资金优先级
    enabled VARCHAR,                -- 启用状态
    valid VARCHAR,                  -- 路由是否有效 (Y/N)
    -- 其他字段...
);
```

### 4. 拍拍贷路由流程

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/OrderRouterService.java" mode="EXCERPT">
````java
private void refreshRouters(Order order) {
    FlowConfig flowConfigSlave = flowConfigRepository.findByFlowChannel(order.getFlowChannel())
        .orElseThrow(() -> new BizException(ResultCode.FLOW_CONFIG_NOT_EXIST));
    
    List<FlowRouteConfig> flowRouteConfig = flowRouteConfigRepository
        .findByFlowIdAndEnabled(flowConfigSlave.getId(), FlowCapitalEnable.ENABLE);
    flowRouteConfig.sort(Comparator.comparingInt(FlowRouteConfig::getPriority));
}
````
</augment_code_snippet>

**路由步骤**：
1. 订单进入，带有FlowChannel信息（PPCJDL）
2. 根据FlowChannel查找flow_config表获取flowId
3. 根据flowId查找flow_route_config表获取可用资方列表
4. 按priority字段排序，依次尝试各个资方
5. 选中资方后，设置order.bankChannel并进行授信

## 🔄 绿信改造方案

### 1. 绿信当前配置

#### 申请渠道配置
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/ApplyChannel.java" mode="EXCERPT">
````java
public enum ApplyChannel {
    LVXIN("01", "绿信"),
}
````
</augment_code_snippet>

#### 流量渠道配置
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/FlowChannel.java" mode="EXCERPT">
````java
public enum FlowChannel {
    LVXIN("绿信", FlowType.IRR36, "绿信"),
}
````
</augment_code_snippet>

### 2. 改造步骤

#### 步骤1: 数据库配置修改

**查询当前绿信流量配置**：
```sql
SELECT * FROM flow_config WHERE flow_channel = 'LVXIN';
```

**查询湖消资方配置**：
```sql
SELECT * FROM capital_config WHERE bank_channel = 'HXBK';
```

**添加绿信到湖消的路由配置**：
```sql
-- 假设绿信的flow_id为'flow_lvxin_001'，湖消的capital_id为'capital_hxbk_001'
INSERT INTO flow_route_config (
    id, 
    flow_id, 
    capital_id, 
    priority, 
    enabled, 
    valid,
    created_by,
    created_time
) VALUES (
    'route_lvxin_hxbk_001',
    'flow_lvxin_001',        -- 绿信流量ID
    'capital_hxbk_001',      -- 湖消资方ID
    1,                       -- 优先级设为1（最高优先级）
    'Y',                     -- 启用
    'Y',                     -- 有效
    'admin',
    NOW()
);
```

#### 步骤2: 验证配置

**验证路由配置查询**：
```sql
SELECT 
    fc.flow_channel, 
    cc.bank_channel, 
    frc.priority, 
    frc.enabled
FROM flow_config fc
JOIN flow_route_config frc ON fc.id = frc.flow_id
JOIN capital_config cc ON frc.capital_id = cc.id
WHERE fc.flow_channel = 'LVXIN' 
  AND frc.valid = 'Y' 
  AND frc.enabled = 'Y'
ORDER BY frc.priority;
```

### 3. 无需修改的代码部分

#### 路由逻辑
- 路由核心逻辑在`OrderRouterService`中，完全基于数据库配置
- 无需修改任何Java代码

#### 枚举定义
- `FlowChannel.LVXIN`已存在
- `BankChannel.HXBK`已存在
- 无需添加新的枚举值

### 4. 可能需要关注的配置

#### 资方限制配置
确认湖消资方配置是否支持绿信的业务参数：
```sql
SELECT 
    bank_channel,
    periods_range,      -- 期数范围
    ages_range,         -- 年龄范围
    single_amt_range,   -- 单笔金额范围
    enabled
FROM capital_config 
WHERE bank_channel = 'HXBK';
```

#### 流量限额配置
确认绿信流量配置的限额设置：
```sql
SELECT 
    flow_channel,
    credit_day_amt,     -- 授信日限额
    loan_day_amt,       -- 放款日限额
    enabled
FROM flow_config 
WHERE flow_channel = 'LVXIN';
```

## 📝 总结

### 改造工作量
- **数据库配置**：仅需在`flow_route_config`表中添加一条记录
- **代码修改**：无需修改任何Java代码
- **测试验证**：需要验证绿信订单能正确路由到湖消

### 操作步骤
1. 查询绿信flow_id和湖消capital_id
2. 在flow_route_config表中添加路由配置
3. 验证配置正确性
4. 测试绿信订单路由到湖消

### 风险控制
- 建议先在测试环境验证
- 可以通过priority和enabled字段控制路由开关
- 支持多资方配置，可以设置备选资方

这种基于数据库配置的路由机制提供了很好的灵活性，无需修改代码即可实现渠道到资方的路由调整。
