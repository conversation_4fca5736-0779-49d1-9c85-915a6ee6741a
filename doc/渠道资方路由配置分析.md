# 渠道和资方路由配置分析

## 📋 系统概述

这个系统通过多层配置来实现渠道和资方的路由关系，主要涉及以下几个核心概念：

- **ApplyChannel**: 申请渠道（如拍拍贷的不同客群）
- **FlowChannel**: 流量渠道（如PPCJDL代表拍拍贷流量）
- **BankChannel**: 资方渠道（如CYBK长银、HXBK湖消）
- **路由配置**: 通过数据库表配置流量到资方的映射关系

## 🔄 路由配置架构

### 1. 申请渠道 (ApplyChannel)

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/ApplyChannel.java" mode="EXCERPT">
````java
public enum ApplyChannel {
    QHYP_BORUI("qhyp_borui", "博瑞"),
    QHYP_TANZHI("tanzhi", "探知流量"),
    QHYP_ZR("qhyp_zr", "其他"),
    LVXIN("01", "绿信"),
    QHYP_GIO("gio", "gio营销"),
    CJCYDL_PPD2("p002","拍拍贷-低定价客群"),
    CJCYDL_PPD3("p001","拍拍贷-高定价客群")
}
````
</augment_code_snippet>

**拍拍贷渠道配置**：
- `CJCYDL_PPD2("p002","拍拍贷-低定价客群")`
- `CJCYDL_PPD3("p001","拍拍贷-高定价客群")`

### 2. 流量渠道 (FlowChannel)

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/enums/FlowChannel.java" mode="EXCERPT">
````java
public enum FlowChannel {
    LVXIN("绿信", FlowType.IRR36, "绿信"),
    TONG_CHENG("同程", FlowType.IRR36, "同程"),
    PPCJDL("信也--拍拍", FlowType.IRR36, "拍拍贷"),
}
````
</augment_code_snippet>

**拍拍贷流量配置**：
- `PPCJDL("信也--拍拍", FlowType.IRR36, "拍拍贷")`

### 3. 资方渠道 (BankChannel)

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/enums/BankChannel.java" mode="EXCERPT">
````java
public enum BankChannel {
    CYBK("CYBK", "长银直连", new BigDecimal("0.2399")),
    HXBK("HXBK", "湖消 间接连接，通过对接蚂蚁放款", new BigDecimal("0.2399")),
    RL_SUS("RL_SUSHANG", "润楼苏商", new BigDecimal("0.24"))
}
````
</augment_code_snippet>

**当前支持的资方**：
- `CYBK`: 长银消金直连
- `HXBK`: 湖消（通过蚂蚁对接）
- `RL_SUS`: 润楼苏商

## 🗄️ 数据库配置表结构

### 1. flow_config (流量配置表)
```sql
CREATE TABLE flow_config (
    id VARCHAR PRIMARY KEY,
    flow_channel VARCHAR,           -- 流量渠道 (如PPCJDL)
    credit_day_amt DECIMAL,         -- 流量授信限额
    loan_day_amt DECIMAL,           -- 流量放款限额
    first_protocol_channel VARCHAR, -- 第一绑卡渠道
    second_protocol_channel VARCHAR,-- 第二绑卡渠道
    enabled VARCHAR,                -- 启用状态
    -- 其他字段...
);
```

### 2. capital_config (资方配置表)
```sql
CREATE TABLE capital_config (
    id VARCHAR PRIMARY KEY,
    bank_channel VARCHAR,           -- 资方渠道 (如CYBK、HXBK)
    credit_day_limit DECIMAL,       -- 资方授信日限额
    loan_day_limit DECIMAL,         -- 资方放款日限额
    periods_range VARCHAR,          -- 支持期数
    ages_range VARCHAR,             -- 年龄区间
    enabled VARCHAR,                -- 启用状态
    -- 其他字段...
);
```

### 3. flow_route_config (流量路由配置表) - 核心路由表
```sql
CREATE TABLE flow_route_config (
    id VARCHAR PRIMARY KEY,
    flow_id VARCHAR,                -- 流量ID (关联flow_config.id)
    capital_id VARCHAR,             -- 资金ID (关联capital_config.id)
    priority INTEGER,               -- 资金优先级
    enabled VARCHAR,                -- 启用状态
    valid VARCHAR,                  -- 路由是否有效 (Y/N)
    -- 其他字段...
);
```

## 🎯 路由工作原理

### 核心路由逻辑

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/OrderRouterService.java" mode="EXCERPT">
````java
private void refreshRouters(Order order) {
    FlowConfig flowConfigSlave = flowConfigRepository.findByFlowChannel(order.getFlowChannel())
        .orElseThrow(() -> new BizException(ResultCode.FLOW_CONFIG_NOT_EXIST));

    // 根据flowId 查询 flow_route_config
    List<FlowRouteConfig> flowRouteConfig = flowRouteConfigRepository.findByFlowIdAndEnabled(flowConfigSlave.getId(), FlowCapitalEnable.ENABLE);
    flowRouteConfig.sort(Comparator.comparingInt(FlowRouteConfig::getPriority));
    
    // 遍历路由配置，按优先级处理
    flowRouteConfig.forEach(frc -> {
        CapitalConfig capitalConfigSlave = capitalConfigRepository.findById(frc.getCapitalId())
            .orElseThrow(() -> new BizException(ResultCode.ROURE_CAPITAL_CONFIG_NOT_EXIST));
        // 创建路由记录...
    });
}
````
</augment_code_snippet>

### 路由步骤说明

1. **订单进入**: 订单带有FlowChannel信息（如PPCJDL）
2. **查找流量配置**: 根据FlowChannel查找flow_config表获取flowId
3. **查找路由配置**: 根据flowId查找flow_route_config表获取可用资方列表
4. **按优先级路由**: 按priority字段排序，依次尝试各个资方
5. **资方授信**: 选中资方后，设置order.bankChannel并进行授信

## 📊 拍拍贷路由配置示例

### 拍拍贷的完整路由链路

```
申请渠道: CJCYDL_PPD2/CJCYDL_PPD3 (p002/p001)
    ↓
流量渠道: PPCJDL ("信也--拍拍")
    ↓
flow_config表: 查找PPCJDL对应的flowId
    ↓
flow_route_config表: 根据flowId查找可用资方
    ↓
可能的资方: CYBK(长银) 或 HXBK(湖消) 或 RL_SUS(润楼苏商)
```

### 数据库配置示例

假设拍拍贷的配置如下：

**flow_config表**:
```sql
INSERT INTO flow_config (id, flow_channel, enabled) 
VALUES ('flow_001', 'PPCJDL', 'Y');
```

**capital_config表**:
```sql
INSERT INTO capital_config (id, bank_channel, enabled) 
VALUES ('capital_001', 'CYBK', 'Y'),
       ('capital_002', 'HXBK', 'Y');
```

**flow_route_config表**:
```sql
INSERT INTO flow_route_config (id, flow_id, capital_id, priority, enabled, valid) 
VALUES ('route_001', 'flow_001', 'capital_001', 1, 'Y', 'Y'),  -- PPCJDL -> CYBK (优先级1)
       ('route_002', 'flow_001', 'capital_002', 2, 'Y', 'Y');  -- PPCJDL -> HXBK (优先级2)
```

这样配置后，拍拍贷的订单会：
1. 优先尝试长银(CYBK)授信
2. 如果长银失败，再尝试湖消(HXBK)授信

## 🔧 配置管理

### 管理后台查询

<augment_code_snippet path="cash-manage-system/src/main/resources/mapper/FlowRouteConfigMapper.xml" mode="EXCERPT">
````xml
<select id="query" resultType="com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse">
    select a.id, a.priority, b.bank_channel as bankChannel, b.id as capitalId, 
           b.enabled as capitalEnabled, a.enabled, a.valid
    from flow_route_config a
    left join capital_config b on a.capital_id = b.id
    where a.flow_id = #{flowId} and a.valid = 'Y'
    order by a.priority
</select>
````
</augment_code_snippet>

### 配置特点

1. **动态配置**: 通过数据库表配置，无需修改代码
2. **优先级控制**: 通过priority字段控制资方尝试顺序
3. **开关控制**: 通过enabled字段控制路由开关
4. **软删除**: 通过valid字段实现软删除

## 📝 总结

**拍拍贷走哪个资方的配置方式**：

1. **不是写死的**: 配置完全通过数据库表动态管理
2. **配置位置**: 主要在`flow_route_config`表中配置
3. **配置方式**: 通过flowId关联流量，capitalId关联资方，priority控制优先级
4. **灵活性**: 可以配置多个资方作为备选，系统会按优先级依次尝试

要查看拍拍贷当前走哪个资方，需要查询数据库：
```sql
SELECT fc.flow_channel, cc.bank_channel, frc.priority, frc.enabled
FROM flow_config fc
JOIN flow_route_config frc ON fc.id = frc.flow_id
JOIN capital_config cc ON frc.capital_id = cc.id
WHERE fc.flow_channel = 'PPCJDL' 
  AND frc.valid = 'Y' 
  AND frc.enabled = 'Y'
ORDER BY frc.priority;
```
