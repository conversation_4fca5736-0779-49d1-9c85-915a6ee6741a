# Capital对账文件分析

## 📋 概述

对账文件是capital系统中用于与资金方（银行）进行业务数据核对的重要机制。通过对账文件，系统可以确保双方的业务数据一致性，包括放款、还款、代偿等各类业务的金额和状态。

## 🏗️ 核心架构

### 1. 对账文件类型 (FileType)

对账文件主要分为以下几类：

<augment_code_snippet path="src/main/java/com/jinghang/capital/api/dto/FileType.java" mode="EXCERPT">
````java
LOAN_FILE("放款对账文件"),
REPAYMENT_FILE("还款对账文件"),
REPAY_PLAN_FILE("还款计划对账文件"),
COMPENSATION_INNER_MARK_FILE("代偿标记文件"),
COMPENSATION_FILE("代偿文件"),
REPURCHASE_FILE("回购文件"),
DUE_FILE("还款计划担保费文件"),
LOAN_VOUCHER_FILE("放款凭证文件"),
COMPENSATORY_VOUCHER_FILE("代偿凭证文件"),
CREDIT_SETTLE_VOUCHER_FILE("结清凭证文件"),
````
</augment_code_snippet>

### 2. 对账类型 (ReccType)

<augment_code_snippet path="src/main/java/com/jinghang/capital/api/dto/recc/ReccType.java" mode="EXCERPT">
````java
/** 放款 */
LOAN,
/** 还款 */
REPAY,
/** 放款后还款计划*/
PLAN,
/** 还款计划 */
REPAY_PLAN,
/** 代偿 */
COMPENSATION,
/** 预代偿 */
PRE_COMPENSATION,
````
</augment_code_snippet>

## 🗄️ 核心数据结构

### 1. 对账文件主表 (ReconciliationFile)

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/entity/ReconciliationFile.java" mode="EXCERPT">
````java
@Entity
@Table(name = "reconciliation_file")
public class ReconciliationFile extends BaseEntity {
    /** 产品 */
    @Enumerated(EnumType.STRING)
    private ProductVo product;
    /** 资方 */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /** 对账文件类型 */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /** 文件日期 */
    private LocalDate fileDate;
    /** 对账状态 */
    @Enumerated(EnumType.STRING)
    private ReccStateEnum reconciliationState;
````
</augment_code_snippet>

### 2. CYBK对账文件表 (CYBKReconcileFile)

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/entity/CYBKReconcileFile.java" mode="EXCERPT">
````java
@Entity
@Table(name = "cybk_reconcile_file")
public class CYBKReconcileFile extends BaseEntity implements Serializable {
    /** 产品 */
    private String product;
    /** 资方 */
    private String channel;
    /** 对账文件类型 */
    private String reccType;
    /** 文件名称 */
    private String fileName;
    /** 文件日期 */
    private LocalDate fileDate;
    /** 对账状态 */
    private String reccState;
````
</augment_code_snippet>

## 🔄 对账流程

### 1. 对账文件下载流程

#### 1.1 内部对账文件下载
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/inner/service/AbstractRecDownloadService.java" mode="EXCERPT">
````java
public void downLoad(LocalDate fileDate) {
    FileDownloadDto fileDownloadDto = new FileDownloadDto();
    fileDownloadDto.setType(com.jinghang.capital.api.dto.FileType.valueOf(this.reccType().name()));
    fileDownloadDto.setProduct(Product.ZC_CASH);
    fileDownloadDto.setFileDate(fileDate);

    // 下载内部对账文件
    RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
    
    // 对账文件记录主表
    ReconciliationFile recFile = saveRec(fileDate, restResult.getData());

    // 对账
    if (!reccDetail(recFile)) {
        recFile.setRecState(ReccState.F);
        warningService.warn("内部对账文件未对齐:" + recFile.getId());
    } else {
        // 明细全部对齐
        recFile.setRecState(ReccState.S);
    }
````
</augment_code_snippet>

#### 1.2 外部对账文件下载（宝付示例）
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/recc/baofu/BaofuReccFileDownloadJob.java" mode="EXCERPT">
````java
// 1. 下载宝付对账文件
HttpRequest httpRequest = HttpRequest.post(BAOFU_URL)
    .form("member_id", memberId)
    .form("version", "*******")
    .form("file_type", "fi")
    .form("client_ip", clientIp)
    .form("settle_date", fileDate);
String respBody = HttpUtil.exec(httpRequest);

// 2. 上传文件到oss保留一份
String ossKey = OSS_PATH + fileDate.format(DateUtil.SHORT_FORMATTER) + "/" + memberId + "/" + reccFile.getName();
fileService.uploadOss(ossBucket, ossKey, new FileInputStream(reccFile));
````
</augment_code_snippet>

### 2. 对账数据处理

#### 2.1 放款对账处理
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/inner/service/LoanRecDownloadService.java" mode="EXCERPT">
````java
// 对账规则
loanInnerRec.setRecState(ReccState.S);
if (loan.getAmount().compareTo(loanInnerRec.getAmount()) != 0
    || !loan.getPeriods().equals(loanInnerRec.getPeriods())
    || !loan.getLoanState().equals(ProcessState.SUCCEED)) {
    loanInnerRec.setRecState(ReccState.F);
    loanInnerRec.setRemark("金额|期数|状态不对应");
    recState.set(false);
}
````
</augment_code_snippet>

#### 2.2 还款对账处理
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/inner/service/RepaymentRecDownloadService.java" mode="EXCERPT">
````java
if (bankRepayRecord.getPrincipal().compareTo(entity.getPrincipalAmount()) != 0
    || bankRepayRecord.getInterest().compareTo(entity.getInterestAmount()) != 0
    || bankRepayRecord.getGuarantee().compareTo(entity.getGuaranteeAmount()) != 0
    || !bankRepayRecord.getState().equals(ProcessState.SUCCEED)) {
    entity.setRecState(ReccState.F);
    entity.setRemark("金额|还款状态,不匹配");
    recState.set(false);
}
````
</augment_code_snippet>

## 🏦 银行特定实现

### 1. CYBK（长银消金）对账

#### 1.1 对账文件类型
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/enums/CYBKReccFileType.java" mode="EXCERPT">
````java
public enum CYBKReccFileType {
    LOAN_FILE("放款对账文件"),
    REPAYMENT_FILE("还款对账文件"),
    REPAYMENT_DETAIL_FILE("还款后还款计划对账文件"),
    PLAN_FILE("还款计划文件"),
    PRE_COMPENSATION_FILE("预代偿文件"),
    COMPENSATION_FILE("代偿文件");
}
````
</augment_code_snippet>

#### 1.2 对账文件上传到蚂蚁
<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/domain/cybk/CYBKLoanFileHandler.java" mode="EXCERPT">
````java
@Override
public void pushToAnt(ReccUploadAntJobParamEntity jobEntity) {
    LocalDate fileDate = LocalDate.parse(jobEntity.getFileDate());
    boolean needCover = jobEntity.isNeedCover();
    String sftpFilePath = sftpFilePath(fileDate);
    String sftpOkFilePath = sftpFilePath + ".ok";

    logger.info("[长银对账文件上传蚂蚁job]开始处理长银消金直连对账文件: [{}] 和 [{}]", sftpFilePath, sftpOkFilePath);

    super.sftp.custom(channelSftp -> {
        // 1. 处理主csv文件
        processAndUploadFile(channelSftp, sftpFilePath, fileDate, true, needCover);
        // 2. 处理.ok文件（直接上传，不修改内容）
        processAndUploadFile(channelSftp, sftpOkFilePath, fileDate, false, needCover);
    });
}
````
</augment_code_snippet>

### 2. HXBK（湖消银行）对账

HXBK的对账实现遵循类似的模式，包括对账文件的生成、处理和上传。

## 🔧 API接口

### 1. 对账服务接口
<augment_code_snippet path="src/main/java/com/jinghang/capital/api/ReccService.java" mode="EXCERPT">
````java
public interface ReccService {
    /** 处理对账逻辑 */
    @PostMapping("process")
    RestResult<ReccResultDto> process(@RequestBody ReccApplyDto reccApply);

    /** 查询对账结果 */
    @PostMapping("query")
    RestResult<ReccResultDto> query(@RequestBody ReccApplyDto reccApply);

    /** 对账文件下载 */
    @PostMapping("download")
    RestResult<ReccResultDto> download(@RequestBody ReccDownloadDto dto);
}
````
</augment_code_snippet>

### 2. 对账文件申请接口
<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/controller/ReccController.java" mode="EXCERPT">
````java
@Override
public RestResult<ReccFileListDto> fileApply(ReccFileApplyDto dto) {
    logger.info("对账文件申请, channel: {}, fileDate: {}, company: {}", 
                dto.getChannel(), dto.getFileDate(), dto.getCompany().name());
    ReccFileApplyVo vo = ReccConvert.INSTANCE.toFileApplyVo(dto);
    List<CYBKReconcileFile> files = manageService.fileApply(vo);
    // 转换并返回结果
    return RestResult.success(reccFileListDto);
}
````
</augment_code_snippet>

## ⚙️ 定时任务

### 1. 对账文件下载任务
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/job/inner/LoanReccDownloadJob.java" mode="EXCERPT">
````java
@Component
@JobHandler("loanReccDownloadJob")
public class LoanReccDownloadJob extends AbstractJobHandler {
    @Override
    public void doJob(JobParam jobParam) {
        LocalDate fileDate = Optional.ofNullable(jobParam)
            .map(JobParam::getStartDate)
            .orElse(LocalDate.now().minusDays(1L));
        
        recDownloadFactory.getRecDownloadService(FileType.LOAN_FILE).downLoad(fileDate);
    }
}
````
</augment_code_snippet>

### 2. CYBK对账文件拉取任务
<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/job/cybk/recc/CYBKReccFilePullJob.java" mode="EXCERPT">
````java
@Override
public ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception {
    List<String> fileTypes = CollectionUtils.isEmpty(jobEntity.getFileType())
            ? FILE_TYPES : jobEntity.getFileType();

    for (String type : fileTypes) {
        logger.info("对账文件 [{}] 下载 开始。" + type);
        var handler = cybkHandlerFactory.get(type);
        handler.handle(jobEntity);
        logger.info("对账文件 [{}] [{}] [{}]下载 结束。", type, jobEntity.getChannelType(), jobEntity.getFileDate());
    }
    return ReturnT.SUCCESS;
}
````
</augment_code_snippet>

## 📊 业务意义

### 1. 数据一致性保障
- **放款对账**：确保系统记录的放款金额、期数、状态与资金方一致
- **还款对账**：核对还款本金、利息、担保费等各项金额
- **代偿对账**：验证代偿业务的准确性

### 2. 风险控制
- 及时发现数据差异，避免资金损失
- 提供审计跟踪，满足监管要求
- 支持业务异常处理和修复

### 3. 运营支持
- 自动化对账流程，提高效率
- 异常告警机制，及时处理问题
- 历史数据追溯，支持业务分析

## 🚨 异常处理

系统通过WarningService提供统一的异常告警机制：

<augment_code_snippet path="capital-batch/src/main/java/com/jinghang/capital/batch/service/WarningService.java" mode="EXCERPT">
````java
public void warn(String msg, String... at) {
    String traceId = tracer == null ? null : tracer.currentSpan().context().traceIdString();
    String msg2Send = StringUtil.isBlank(traceId) ? msg : traceId + ":" + msg;
    NotifyMsgSender.useWeWork(this.wxWarningKey).withText(msg2Send).mentionedMobileList(at).send();
}
````
</augment_code_snippet>

当对账出现异常时，系统会通过企业微信发送告警消息，确保相关人员及时处理。
