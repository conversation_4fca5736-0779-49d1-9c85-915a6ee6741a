/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import com.jinghang.cash.modules.project.domain.GenTest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.project.service.GenTestService;
import com.jinghang.cash.modules.project.domain.dto.GenTestQueryCriteria;
import com.jinghang.cash.modules.project.mapper.GenTestMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jinghang.cash.utils.PageUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-26
**/
@Service
@RequiredArgsConstructor
public class GenTestServiceImpl extends ServiceImpl<GenTestMapper, GenTest> implements GenTestService {

    private final GenTestMapper genTestMapper;

    @Override
    public PageResult<GenTest> queryAllPage(GenTestQueryCriteria criteria){
        Page<GenTest> page = new Page<>(criteria.getPage(), criteria.getSize());
        LambdaQueryWrapper<GenTest> wrapper = new LambdaQueryWrapper<GenTest>();
        // TODO: 根据实际需要添加查询条件
        page = genTestMapper.selectPage(page, wrapper);
        return PageUtil.toPage(page.getRecords(), page.getTotal());
    }

    @Override
    public List<GenTest> queryAll(GenTestQueryCriteria criteria){
        return genTestMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(GenTest resources) {
        genTestMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GenTest resources) {
        GenTest genTest = getById(resources.getId());
        genTest.copy(resources);
        genTestMapper.updateById(genTest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<Integer> ids) {
        genTestMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<GenTest> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (GenTest genTest : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("描述", genTest.getRemark());
            list.add(map);
        }
        com.jinghang.cash.utils.FileUtil.downloadExcel(list, response);
    }
}