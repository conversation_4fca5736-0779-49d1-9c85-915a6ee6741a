<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.cash.modules.project.mapper.GenTestMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.GenTest">
        <id column="id" property="id"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, remark
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gen_test
        <where>
        </where>
        order by id desc
    </select>
</mapper>