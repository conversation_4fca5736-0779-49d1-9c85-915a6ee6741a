package com.jinghang.capital.core.banks.cybk.service;

import com.alibaba.fastjson2.JSONObject;
import com.jinghang.capital.api.dto.FundingModel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.QuotaStage;
import com.jinghang.capital.core.banks.AbstractBankCreditService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.convert.CYBKCreditConvert;
import com.jinghang.capital.core.banks.cybk.dto.CYBKCallBackCommonResult;
import com.jinghang.capital.core.banks.cybk.dto.CYBKRemoteResponse;
import com.jinghang.capital.core.banks.cybk.dto.CYBKResponseHeader;
import com.jinghang.capital.core.banks.cybk.dto.credit.*;
import com.jinghang.capital.core.banks.cybk.enums.*;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.convert.EnumConvert;
import com.jinghang.capital.core.convert.entityvo.VoCreditConvert;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.dto.QuotaAdjustApplyDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.remote.manage.ProjectAgreementFeign;
import com.jinghang.capital.core.repository.*;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.credit.*;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Service
@Qualifier("CYBKCreditService")
public class CYBKCreditService extends AbstractBankCreditService {

	public static final int CREDIT_STAGE_FILE_NUM = 2;
	public static final int END_DAY_HOUR = 23;
	public static final int END_DAY_MINUTE = 59;
	public static final int CREDIT_TERM_OF_VALIDITY = 15;
	public static final int RIGHT_ONE = 1;
	public static final List<String> TAIL_PADDING_LIST = List.of("市", "县", "乡", "镇", "区", "路", "道", "线");
	public static final String PADDING_CHAR = "组";
	private static final Logger logger = LoggerFactory.getLogger(CYBKCreditService.class);
	/**
	 * 授信失败异常Code
	 */
	private static final List<String> FAILURE_CODE_LIST = List.of("A002");
	private static final int SEVEN = 7;
	private static final int ID_AGE_START = 6;
	private static final int ID_AGE_END = 14;
	private static final int SIX = 6;
	private static final int TWELVE = 12;
	private static final int IP_START_INDEX = 1;
	private static final int IP_START_END = 1246;
	private static final BigDecimal CUSTOM_YEAR_DAYS = BigDecimal.valueOf(365);
	private static final int PROCESSING_POINT_NUM = 6;
	/**
	 * 身份证长期有效期
	 */
	private static final LocalDate ID_CARD_EXPIRE_PERMANENT = LocalDate.of(2099, 1, 1);
	private static final LocalDate CYBK_PERMANENT = LocalDate.of(9999, 12, 31);
	private static final String CARDNO = "6217001180039821270";
	private static final int TEN = 10;
	private static final int ONE = 1;
	private static final int FOUR = 4;
	/**
	 * 获取客户风险等级
	 *
	 * @param creditScore
	 * @return 1000~400：A；400~300：B;300~200：C；200~0：D;
	 */
	private final int oneThousand = 1000;
	private final int fourHundred = 400;
	private final int threeHundred = 300;
	private final int twoHundred = 200;
	@Autowired
	private CommonService commonService;
	private CYBKLocalValid cybkLocalValid;
	private CYBKRequestService requestService;
	private AccountRepository accountRepository;
	private AccountContactInfoRepository accountContactInfoRepository;
	private AgreementSignatureRepository agreementSignatureRepository;
	@Autowired
	private CYBKPreCreditRepository cybkPreCreditRepository;
	@Autowired
	private WarningService warningService;
	@Autowired
	private CYBKCreditFlowRepository cybkCreditFlowRepository;
	@Autowired
	private CYBKRequestService cybkRequestService;
	@Autowired
	private MqService mqService;
	@Autowired
	private CreditRepository creditRepository;
	@Autowired
	private FileService fileService;
	@Autowired
	private CYBKConfig config;
	@Autowired
	private FinCreditService finCreditService;
	@Autowired
	private QuotaCycleUserInfoRepository quotaCycleUserInfoRepository;
	@Autowired
	private QuotaAdjustRecordRepository quotaAdjustRecordRepository;
	@Autowired
	private ProjectAgreementFeign projectAgreementFeign;

	@Override
	protected CreditResultVo bankCreditQuery(Credit credit, CreditQueryVo queryVo) {
		CYBKCreditQueryRequest creditQueryRequest = new CYBKCreditQueryRequest();
		// 长银直连返回的授信编号
		CYBKCreditFlow creditFlow;

		if (credit.getCreditNo() == null) {
			//超时处理
			creditFlow = cybkCreditFlowRepository.findByCreditId(credit.getId()).orElse(null);
		} else {
			creditFlow = cybkCreditFlowRepository.findByCreditNo(credit.getCreditNo()).orElse(null);
		}

		creditQueryRequest.setOutApplSeq(credit.getId());
		creditQueryRequest.setApplCde(credit.getCreditNo());

		logger.info("长银直连授信申请进度查询请求:{}", JsonUtil.toJsonString(creditQueryRequest));
		CYBKCreditQueryResponse response = requestService.creditQuery(creditQueryRequest, credit.getGuaranteeCompany());
		logger.info("长银直连授信申请进度查询响应:{}", JsonUtil.toJsonString(response));
		CreditResultVo creditResultVo = CYBKCreditConvert.INSTANCE.toVo(response);
		creditResultVo.setCreditAmt(credit.getCreditAmt());
		if (ProcessStatus.SUCCESS.equals(creditResultVo.getStatus())) {
			creditResultVo.setPassTime(LocalDateTime.now());
			creditResultVo.setCapExpireTime(LocalDate.now().plusDays(CREDIT_TERM_OF_VALIDITY).atTime(END_DAY_HOUR, END_DAY_MINUTE, END_DAY_MINUTE));
			creditResultVo.setCreditNo(response.getApplCde());
			if (creditFlow != null) {
				creditFlow.setFundingModel(FundingModel.ALONE);
				creditFlow.setCreditNo(response.getApplCde());
				creditFlow.setCustId(response.getCustId());
				cybkCreditFlowRepository.save(creditFlow);
			}
		}
		return creditResultVo;
	}

	@Override
	public CreditResultVo apply(CreditApplyVo<ExtInfoVo> apply) {
		logger.info("credit-apply:{}", JSONObject.toJSONString(apply));
		// 30天内授信失败检验
		List<Credit> creditList = finCreditService.queryThirtyDayCreditFailRecord(apply.getBankChannel(), apply.getIdCardInfo().getCertNo());
		if (!CollectionUtils.isEmpty(creditList)) {
			throw new BizException(BizErrorCode.CREDIT_FAIL_IN_THIRTY_DAY);
		}

		// 授信记录
		Credit credit = bankCreditValidate(apply);
		if (Objects.isNull(credit)) {
			credit = commonService.creditApply(apply);
			// 设置新客或者老客
			credit.setProductType(getProductType());
			credit = commonService.saveCredit(credit);
		}
		if (CreditStatus.SUCCESS.equals(credit.getCreditStatus()) || CreditStatus.FAIL.equals(credit.getCreditStatus())) {
			// 循环额度 | 调额
			return VoCreditConvert.INSTANCE.toCreditResultDto(credit);
		}
		logger.info("credit-applyChannel:{},CertNo:{}", apply.getBankChannel(), apply.getIdCardInfo().getCertNo());
		//查询正常授信过的记录
		Credit existCredit = finCreditService.querySuccessCreditRecord(apply.getBankChannel(), apply.getIdCardInfo().getCertNo());
		logger.info("apply.getIdCardInfo().getCertNo()-existCredit:{}", JSONObject.toJSONString(existCredit));
		//说明是老用户，需判断是否需要调额
		if (Objects.nonNull(existCredit)) {
			logger.info("laoyonghu-existCredit:{}", JSONObject.toJSONString(existCredit));
			CYBKCreditFlow cybkCreditFlow = cybkCreditFlowRepository.findByCreditId(existCredit.getId()).orElse(null);
			QuotaQueryDto dto = new QuotaQueryDto();
			dto.setStage(QuotaStage.CREDIT);
			dto.setBusinessId(credit.getId());
			dto.setApplCde(existCredit.getCreditNo());
			credit.setCreditNo(existCredit.getCreditNo());
			credit.setCreditContractNo(existCredit.getCreditContractNo());
			credit.setCreditType(CreditType.ADJUST_CREDIT);
			commonService.saveCredit(credit);
			saveQuotaCycleUserInfo(existCredit, credit.getId(), apply.getIdCardInfo().getCertValidEnd(), cybkCreditFlow);
			mqService.submitQuotaQuery(JsonUtil.toJsonString(dto));
		} else {
			// 异步授信申请
			mqService.submitCreditApply(credit.getId());
		}
		return VoCreditConvert.INSTANCE.toCreditResultDto(finCreditService.getCredit(credit.getId()));
	}

	@Override
	protected CreditResultVo bankCreditApply(Credit credit) {

		CreditResultVo creditResultVo = new CreditResultVo();
		//授信阶段签章文件数
		List<ProjectAgreementDto> agreementDtos = projectAgreementFeign.getByStage(credit.getProjectCode(), "", LoanStage.CREDIT.name());
		if (CollectionUtils.isEmpty(agreementDtos)) {
			logger.error("项目合同配置表为空,项目唯一编码为:{}", credit.getProjectCode());
			throw new BizException(BizErrorCode.AGREEMENT_NOT_CONFIG);
		}
		//过滤是否融担代签的合同协议
		agreementDtos = agreementDtos.stream().filter(agreement -> ActiveInactive.Y.equals(agreement.getIsRdSignature())).toList();
		Account account = getCommonService().findAccountById(credit.getAccountId());
		for (ProjectAgreementDto agreementDto : agreementDtos) {
			AgreementSignature agreementSignature = generateAgrSignature(credit.getId(), FileType.valueOf(agreementDto.getContractTemplateType().name()), SignatureType.TEMPLATE);
			agreementSignature.setBankMobilePhone(account.getMobile());
			agreementSignature.setIdentNo(account.getCertNo());
			agreementSignature.setPersonName(account.getName());
			agreementSignature.setTemplateNo(agreementDto.getTemplateNo());
			agreementSignature.setAddress(account.getLivingAddress());
			AgreementSignature saved = agreementSignatureRepository.save(agreementSignature);
			//签章申请监听
			getMqService().signatureApply(saved.getId());
		}

		creditResultVo.setStatus(ProcessStatus.PROCESSING);
		return creditResultVo;
	}

	// 预授信
	@Override
	public PreCreditApplyResultVo preApply(PreCreditApplyVo preApply) {
		CYBKPreCreditRequest request = new CYBKPreCreditRequest();
		GuaranteeCompany guaranteeCompany = GuaranteeCompany.CJRD;
		request.setIdNoMD5(preApply.getCardNo());
		request.setMobileMD5(preApply.getMobile());
		request.setProductCode(config.getLoanType(guaranteeCompany));
		logger.info("长银直连预授信, 请求参数: {}", JsonUtil.toJsonString(request));
		CYBKPreCreditResponse cybkPreCreditResponse = cybkRequestService.preCreditApply(request);
		logger.info("长银直连预授信, 响应参数: {}", JsonUtil.toJsonString(cybkPreCreditResponse));
		PreCreditApplyResultVo vo = CYBKCreditConvert.INSTANCE.toVo(cybkPreCreditResponse);
		if (cybkPreCreditResponse.getOutRiskMsg() != null && cybkPreCreditResponse.getOutRiskMsg().length() > 0) {
			vo.setRuleDesc(cybkPreCreditResponse.getOutRiskMsg());
		} else {
			vo.setRuleDesc(CYBKResultType.fromCode(vo.getRuleCode()).getDescription());
		}
		CYBKPreCredit cybkPreCredit = new CYBKPreCredit();
		cybkPreCredit.setIdNo(preApply.getCardNo());
		cybkPreCredit.setMobile(preApply.getMobile());
		cybkPreCredit.setProductCode(config.getLoanType(guaranteeCompany));
		cybkPreCredit.setResult(cybkPreCreditResponse.getResult());
		cybkPreCredit.setIsStock(cybkPreCreditResponse.getIsStock());
		cybkPreCredit.setOutRiskCode(cybkPreCreditResponse.getOutRiskCode());
		cybkPreCredit.setOutRiskMsg(cybkPreCreditResponse.getOutRiskMsg());
		cybkPreCredit.setCreateTime(LocalDateTime.now());
		cybkPreCreditRepository.save(cybkPreCredit);
		return vo;
	}

	@Override
	protected void bankCreditApplyResult(Credit credit, CreditResultVo resultVo) {
		credit.setCreditStatus(EnumConvert.INSTANCE.toCreditStatus(resultVo.getStatus()));
		getCommonService().saveCredit(credit);

		if (resultVo.getStatus() == ProcessStatus.PROCESSING) {
			sendContractUploadMessage(credit.getId());
		}

	}

	private void saveQuotaCycleUserInfo(Credit credit, String creditId, LocalDate cartEnd, CYBKCreditFlow cybkCreditFlow) {
		QuotaCycleUserInfo quotaCycleUserInfo2 = quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
				credit.getChannel(), credit.getCustCertNo()).orElse(null);
		if (quotaCycleUserInfo2 != null) {
			quotaCycleUserInfo2.setCreditId(creditId);
			quotaCycleUserInfo2.setUserIdCard(credit.getCustCertNo());
			quotaCycleUserInfo2.setChannel(credit.getChannel());
			quotaCycleUserInfo2.setCreditSeq(credit.getId());
			quotaCycleUserInfo2.setBankCreditSeq(credit.getCreditNo());
			quotaCycleUserInfo2.setCertValidEnd(cartEnd);
			if (cybkCreditFlow != null) {
				quotaCycleUserInfo2.setBankUserId(cybkCreditFlow.getCustId());
			}
			quotaCycleUserInfoRepository.save(quotaCycleUserInfo2);
		} else {
			QuotaCycleUserInfo quotaCycleUserInfo = new QuotaCycleUserInfo();
			quotaCycleUserInfo.setCreditId(creditId);
			quotaCycleUserInfo.setUserIdCard(credit.getCustCertNo());
			quotaCycleUserInfo.setChannel(credit.getChannel());
			quotaCycleUserInfo.setCreditSeq(credit.getId());
			quotaCycleUserInfo.setBankCreditSeq(credit.getCreditNo());
			quotaCycleUserInfo.setCertValidEnd(cartEnd);
			if (cybkCreditFlow != null) {
				quotaCycleUserInfo.setBankUserId(cybkCreditFlow.getCustId());
			}
			quotaCycleUserInfoRepository.save(quotaCycleUserInfo);
		}
	}

	/**
	 * 上传资方授信阶段合同， 监听入口
	 * <p>
	 * 需要调用监测文件是否签署成功
	 *
	 * @param creditId 授信id
	 */
	@Override
	public void contractUpload(String creditId) {
		Credit credit = getCommonService().findCreditById(creditId);
		//授信阶段签章文件数
		String projectCode = credit.getProjectCode();
		List<ProjectAgreementDto> projectAgreementDtos = projectAgreementFeign.getByStage(projectCode, "", LoanStage.CREDIT.name());
		if (CollectionUtils.isEmpty(projectAgreementDtos)) {
			logger.error("项目合同配置表为空,项目唯一编码为:{}", credit.getProjectCode());
			throw new BizException(BizErrorCode.AGREEMENT_NOT_CONFIG);
		}
		//过滤是否融担代签的合同协议
		projectAgreementDtos = projectAgreementDtos.stream().filter(agreement -> ActiveInactive.Y.equals(agreement.getIsRdSignature())).toList();
		List<FileType> fileTypes = projectAgreementDtos.stream().map(agreement -> FileType.valueOf(agreement.getContractTemplateType().name())).toList();
		//判断授信前签章是否签署成功
		List<LoanFile> loanFiles = fetchAllNeedFile(credit, fileTypes);
		logger.info("授信文件: {}", JSONObject.toJSONString(loanFiles));
		if (loanFiles.size() != CREDIT_STAGE_FILE_NUM) {
			logger.info("CYBK credit sign file not ok, creditId: {}", credit);
			sendContractUploadDelayMessage(creditId);
			return;
		}
		GuaranteeCompany guaranteeCompany = credit.getGuaranteeCompany();
		// 重新查询所有需要上传的协议文件
		List<LoanFile> creditFiles = getCommonService().getLoanFileRepository().findByCreditId(credit.getId());
		Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
		List<CYBKImageInfo> cybkImageInfos = uploadFile(creditFiles, account.getCertNo(),guaranteeCompany);
		if(!CollectionUtils.isEmpty(cybkImageInfos)){
			for(CYBKImageInfo info:cybkImageInfos){
				if (ObjectUtils.isEmpty(info.getImageUrl())){
					sendContractUploadDelayMessage(creditId);
					return;
				}
			}
		}
		CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(credit.getId()).orElse(new CYBKCreditFlow());
		creditFlow.setCreditId(creditId);
		CYBKCreditFlow creditFlowSave = cybkCreditFlowRepository.save(creditFlow);
		// 组装授信参数
		CYBKCreditApplyRequest request = buildApplyRequest(credit, cybkImageInfos);
		// 申请资方授信
		logger.info("长银直连申请授信, 组装后参数: {}", JsonUtil.toJsonString(request));
		CYBKRemoteResponse response = requestService.creditApply(request, credit.getGuaranteeCompany());
		logger.info("长银直连申请授信, 响应后参数: {}", JsonUtil.toJsonString(response));
		if (Objects.isNull(response)) {
			// 丢延迟队列，异步查询授信结果
			getMqService().submitCreditResultQueryDelay(creditId);
			return;
		}
		CYBKResponseHeader respHead = response.getHead();
		if (FAILURE_CODE_LIST.contains(respHead.getRespCode())) {
			//授信失败处理
			creditFail(credit, respHead.getRespMsg());
			return;
		}
		// 正常发起授信后,结果处理
		creditProcessing(response, creditFlowSave, credit);
		// 丢延迟队列，异步查询授信结果
		getMqService().submitCreditResultQueryDelay(creditId);
	}

	private void creditProcessing(CYBKRemoteResponse response, CYBKCreditFlow creditFlowSave, Credit credit) {
		try {
			CYBKCreditApplyResponse creditResp = JsonUtil.convertToObject(JsonUtil.convertToString(response.getBody()), CYBKCreditApplyResponse.class);
			// 请求成功，直接设置处理中，以查询结果为最终状态
			creditFlowSave.setCreditNo(creditResp.getApplCde());
			creditFlowSave.setCustId(creditResp.getCustId());
			cybkCreditFlowRepository.save(creditFlowSave);
			credit.setCreditNo(creditResp.getApplCde());
			credit.setCreditStatus(CreditStatus.PROCESSING);
			getCommonService().saveCredit(credit);
		} catch (Exception e) {
			logger.error("长银直连 授信结果转换失败", e);
		}
	}

	private void creditFail(Credit credit, String failReason) {
		credit.setRemark(failReason);
		credit.setCreditStatus(CreditStatus.FAIL);
		getCommonService().saveCredit(credit);
	}

	private void sendContractUploadMessage(String businessId) {
		// 监测授信阶段文件
		ContractBizDto contractBizDto = new ContractBizDto();
		contractBizDto.setStage(LoanStage.CREDIT);
		contractBizDto.setBusinessId(businessId);
		getMqService().submitContractUpload(JsonUtil.toJsonString(contractBizDto));
	}

	private void sendContractUploadDelayMessage(String businessId) {
		// 监测授信阶段文件
		ContractBizDto contractBizDto = new ContractBizDto();
		contractBizDto.setStage(LoanStage.CREDIT);
		contractBizDto.setBusinessId(businessId);
		getMqService().submitContractUploadDelay(JsonUtil.toJsonString(contractBizDto));
	}

	@Override
	public void creditAgreementRecreate(Credit credit) {
		//授信阶段签章文件数
		List<ProjectAgreementDto> projectAgreementDtos = projectAgreementFeign.getByStage(credit.getProjectCode(), "", LoanStage.CREDIT.name());
		if (CollectionUtils.isEmpty(projectAgreementDtos)) {
			logger.error("项目合同配置表为空,项目唯一编码为:{}", credit.getProjectCode());
			throw new BizException(BizErrorCode.AGREEMENT_NOT_CONFIG);
		}
		//过滤是否融担代签的合同协议
		projectAgreementDtos = projectAgreementDtos.stream().filter(agreement -> ActiveInactive.Y.equals(agreement.getIsRdSignature())).toList();

		//调用签章签署,保存协议文件
		Account account = getCommonService().findAccountById(credit.getAccountId());
		for (ProjectAgreementDto agreementDto : projectAgreementDtos) {
			AgreementSignature agreementSignature = generateAgrSignature(credit.getId(), FileType.valueOf(agreementDto.getContractTemplateType().name()), SignatureType.TEMPLATE);
			agreementSignature.setBankMobilePhone(account.getMobile());
			agreementSignature.setIdentNo(account.getCertNo());
			agreementSignature.setPersonName(account.getName());
			agreementSignature.setTemplateNo(agreementDto.getTemplateNo()); //文件模板编号
			agreementSignature.setAddress(account.getLivingAddress());
			AgreementSignature saved = agreementSignatureRepository.save(agreementSignature);
			//签章申请监听
			getMqService().signatureApply(saved.getId());
		}
	}

	private List<AgreementSignature> fetchAgreementNeedFile(Credit credit) {
		List<ProjectAgreementDto> agreementDtos = projectAgreementFeign.getByStage(credit.getProjectCode(), "", LoanStage.CREDIT.name());
		List<FileType> fileTypes = new ArrayList<>(agreementDtos.stream().map(agreement -> FileType.valueOf(agreement.getContractTemplateType().name())).toList());
		return agreementSignatureRepository.findByChannelAndLoanStageAndFileTypeList(credit.getId(), BankChannel.CYBK, LoanStage.CREDIT, fileTypes);
	}

	//签章信息
	private AgreementSignature generateAgrSignature(String creditId, FileType fileType, SignatureType signatureType) {
		AgreementSignature signature = new AgreementSignature();
		signature.setBusinessId(creditId);
		signature.setChannel(BankChannel.CYBK);
		signature.setFileType(fileType);
		signature.setSignState(ProcessStatus.INIT);
		signature.setLoanStage(LoanStage.CREDIT);
		signature.setSignatureType(signatureType);
		signature.setDynamicOssBucket("");
		signature.setDynamicOssKey("");
		return signature;
	}

	//查询签署完成协议文件
	private List<LoanFile> fetchAllNeedFile(Credit credit, List<FileType> fileTypes) {
		return getCommonService().getLoanFileRepository().findByCreditIdAndFileTypeList(credit.getId(), LoanStage.CREDIT.name(), fileTypes);
	}

    private CYBKCreditApplyRequest buildApplyRequest(Credit credit,   List<CYBKImageInfo> imageInfoList) {
        logger.info("长银直连申请授信开始, 开始授信数据: {}", JsonUtil.toJsonString(credit));
        CYBKCreditApplyRequest request = new CYBKCreditApplyRequest();
		GuaranteeCompany guaranteeCompany = credit.getGuaranteeCompany();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
        List<AccountContactInfo> accountContactInfos = accountContactInfoRepository.findByAccountId(account.getId());
        request.setApptTyp("01");
        request.setIdTyp("20");
        request.setCustName(account.getName());
        request.setIdNo(account.getCertNo());
        request.setIndivMobile(account.getMobile());
        request.setOutCustId(account.getId());
        request.setDirectFlag("N"); //非断直连
        request.setIdNoStartDate(account.getCertValidStart().format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setIdNoEndDate(calcCertValidDate(account.getCertValidEnd()).format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setIdOrgan(account.getCertSignOrg());

		// 性别
		request.setIndivSex(CYBKGender.getCodeByGender(account.getGender()));
		//出生日期
		LocalDate bornDate = LocalDate.parse(account.getCertNo().substring(ID_AGE_START, ID_AGE_END), DateTimeFormatter.BASIC_ISO_DATE);
		request.setBornDate(bornDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
		request.setApptAge(String.valueOf(Period.between(bornDate, LocalDate.now()).getYears()));
		CYBKMarriage cybkMarriage = CYBKMarriage.getEnumByMarriage(account.getMarriage());
		request.setIndivMarital(cybkMarriage.getCode());
		request.setIndivEdu(CYBKEduLevel.getCodeByEducation(account.getEducation()));
        request.setIndivDegree(CYBKEduDegree.getEduDegreeCodeByEducation(account.getEducation()));

		// 贷款信息
		CYBKLoanInfo loanInfo = new CYBKLoanInfo();
		loanInfo.setOutApplSeq(credit.getId());
		loanInfo.setApplyDt(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
		loanInfo.setTypGrp("02"); //自主支付贷
		loanInfo.setLoanTyp(config.getLoanType(guaranteeCompany));
		loanInfo.setApplyAmt(credit.getCreditAmt().toPlainString());
		loanInfo.setApplyTnr(credit.getPeriods().toString());
		loanInfo.setPriceIntRat(credit.getBankRate().toPlainString());
		loanInfo.setCustDayRate(credit.getCustomRate().toPlainString());
		loanInfo.setMtdCde("SYS002"); //等额本息
		loanInfo.setLoanFreq("1M"); //1个月
		loanInfo.setDueDayOpt("1"); //放款日对日

		// 添加人脸识别渠道与分数
		if (!FlowChannel.FQLQY001.name().equals(credit.getFlowChannel().name())) {
			//分期乐没有人脸渠道和分数
			loanInfo.setFaceScore(account.getFaceScore().toString());
			loanInfo.setFaceSource(CYBKVendorEnum.OTH.getCode());
		}
		loanInfo.setTerminalType(config.getMerchantTerminal(guaranteeCompany));
		/*商户码*/
		loanInfo.setMerchantNo(config.getMerchantCode(guaranteeCompany));
		/*门店码值 */
		loanInfo.setStoreCode(config.getMerchantShop(guaranteeCompany));
		//银行卡信息可不传
		//CYBKAccountInfo accInfoList = new CYBKAccountInfo();
		//回调地址
		loanInfo.setCallbackUrl(config.getCallBackUrl(guaranteeCompany) + CYBKTradeCode.CREDIT_APPLY_CALL_BACK.getTradeCode());

		CYBKJobInfo jobInfo = new CYBKJobInfo();
		jobInfo.setPositionOpt("10"); //受薪人士
		jobInfo.setIndivPosition("09"); //正式员工
		jobInfo.setProfession(CYBKOccupation.getCYBKCodeByPosition(account.getPosition()));
		jobInfo.setBelongsIndus(CYBKIndustry.getEnumByIndustry(account.getIndustry()).name());
		if (account.getUnit() != null && !account.getUnit().isEmpty()) {
			jobInfo.setIndivEmpName(account.getUnit());
		} else {
			jobInfo.setIndivEmpName("未知");
		}


		CYBKAddress liveAddress = new CYBKAddress();
		liveAddress.setProvince(account.getLivingProvinceCode());
		liveAddress.setCity(account.getLivingCityCode());
		liveAddress.setArea(account.getLivingDistrictCode());
		liveAddress.setAddress(account.getLivingAddress());
		CYBKAddress regAddInfo = new CYBKAddress();
		regAddInfo.setProvince(account.getProvinceCode());
		regAddInfo.setCity(account.getCityCode());
		regAddInfo.setArea(account.getDistrictCode());
		regAddInfo.setAddress(account.getCertAddress());

		CYBKFamilyInfo familyInfo = new CYBKFamilyInfo();
		familyInfo.setLiveAddInfo(liveAddress);
		familyInfo.setRegAddInfo(regAddInfo);

		// 配偶信息  非必填
		// CYBKSpouseInfo spouseInfo = new CYBKSpouseInfo();
		// 联系人
		List<CYBKRelationInfo> contactInfos = new ArrayList<>();
		accountContactInfos.forEach(c -> {
			CYBKRelationInfo contactInfo = new CYBKRelationInfo();
			contactInfo.setRelName(c.getName());
			contactInfo.setRelMobile(c.getPhone());
			contactInfo.setRelRelation(CYBKContactType.getCodeByRelation(c.getRelation()));
			contactInfos.add(contactInfo);
		});
		//List<CYBKImageInfo> imageInfoList = uploadFile(creditFiles, request.getIdNo(), credit.getGuaranteeCompany());

		// 担保信息
		//CYBKDeviceInfo deviceInfo = new CYBKDeviceInfo();
		CYBKExtendInfo cybkExtendInfo = new CYBKExtendInfo();
		cybkExtendInfo.setCustSource(credit.getApplyChannel());
		request.setExtendInfo(cybkExtendInfo);
		request.setLoanInfo(loanInfo);
		request.setOccupationInfo(jobInfo);
		request.setFamilyInfo(familyInfo);
		request.setRelationList(contactInfos);
		request.setImageInfoList(imageInfoList);

		return request;
	}

	List<CYBKImageInfo> uploadFile(List<LoanFile> creditFiles, String idNo, GuaranteeCompany guaranteeCompany) {
		List<CYBKImageInfo> imageInfoList = new ArrayList<>();
		creditFiles.forEach(lf -> {
			CYBKFileType fileType = CYBKFileType.getEnumByFileType(lf.getFileType());
			if (Objects.nonNull(fileType)) {
				CYBKImageInfo imageInfo = new CYBKImageInfo();
				imageInfo.setImageStage("2"); //授信阶段
				imageInfo.setImageName(fileType.getFileName());
				imageInfo.setImageType(fileType.getCode());
				String respNo = "";
				try {
					logger.info("长银文件上授信文件, 组装后参数: {}", JsonUtil.toJsonString(lf));
					respNo = cybkRequestService.sendImage(lf, idNo, guaranteeCompany);
					logger.info("长银文件上授信文件, 返回结果: {}", respNo);
				} catch (Exception e) {
					logger.error("长银文件上传授信信息失败：{}", e.getMessage());
				}
				imageInfo.setImageUrl(respNo);
				imageInfoList.add(imageInfo);
			}
		});
		return imageInfoList;
	}

	@Override
	public CreditResultVo recreditApply(RecreditApplyVo recreditApply) {

		Credit credit = getFinCreditService().getCreditByOuterId(recreditApply.getSysId());
		if (CreditStatus.SUCCESS != credit.getCreditStatus()) {
			throw new BizException(BizErrorCode.RECREDIT_STATUS_ERROR);
		}
		String creditId = credit.getId();

		// 删除之前的合同
		List<LoanFile> loanFiles = fetchNeedDeleteFile(credit);
		getCommonService().getLoanFileRepository().deleteAllInBatch(loanFiles);

		// 更新授信记录为授信中
		credit.setApplyTime(LocalDateTime.now());
		credit.setCreditStatus(CreditStatus.PROCESSING);
		credit.setCreditNo(null);
		credit.setCreditResultAmt(null);
		credit.setCapExpireTime(null);
		credit.setPassTime(null);
		getCommonService().saveCredit(credit);

		// 异步授信申请
		mqService.submitCreditApply(credit.getId());

		CreditResultVo resultVo = new CreditResultVo();
		resultVo.setStatus(ProcessStatus.PROCESSING);
		resultVo.setCreditId(creditId);
		return resultVo;
	}

	@Override
	public void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto) {
		if (QuotaStage.CREDIT.equals(dto.getStage())) {
			String creditId = dto.getBusinessId();
			Credit credit = finCreditService.getCredit(creditId);
			BigDecimal loanAmt = credit.getLoanAmt();
			String adjustBatchNo = IdGenUtil.genReqNo("QAB");
			//额度状态正常
			if (StringUtils.equals(resultDto.getQuotaStatus(), QuotaStatus.NORMAL.name())) {
				//贷款金额小于剩余授信金额，直接放款
				if (loanAmt.compareTo(resultDto.getBalanceQuota()) <= 0) {
					credit.setCreditStatus(CreditStatus.SUCCESS);
					if (StringUtils.isNotBlank(resultDto.getEndDate())) {
						credit.setCapExpireTime((LocalDate.parse(resultDto.getEndDate())).atTime(END_DAY_HOUR, END_DAY_MINUTE, END_DAY_MINUTE));
						credit.setPassTime(LocalDateTime.now());
						credit.setCreditResultAmt(resultDto.getCreditQuota());
					}
					finCreditService.updateCredit(credit);
					QuotaCycleUserInfo quotaCycleUserInfo = quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
							credit.getChannel(), credit.getCustCertNo()).orElse(null);
					if (quotaCycleUserInfo != null) {
						saveCreditFlow(creditId, quotaCycleUserInfo.getBankCreditSeq(), quotaCycleUserInfo.getBankUserId());
					}
				} else { //调额 调整后的额度为
					QuotaAdjustApplyDto adjustApplyDto = new QuotaAdjustApplyDto();
					adjustApplyDto.setAdjustType(AdjustBusinessType.ADJUSTAMTAPPLY.getCode());
					adjustApplyDto.setCreditId(creditId);
					//原有授信额度+本次需调整额度
					BigDecimal newLimitAmt = loanAmt.subtract(resultDto.getBalanceQuota()).add(resultDto.getCreditQuota());
					QuotaAdjustRecord quotaAdjustRecord = saveQuotaQuotaAdjustRecord(credit, resultDto, newLimitAmt, adjustBatchNo);
					adjustApplyDto.setNewLimitAmt(newLimitAmt);
					adjustApplyDto.setAdjustId(quotaAdjustRecord.getId());
					adjustApplyDto.setOutAdjustNo(quotaAdjustRecord.getId());
					adjustApplyDto.setAdjustNo(adjustBatchNo);
					adjustApplyDto.setApplCde(quotaAdjustRecord.getBankCreditSeq());
					adjustApplyDto.setNewLimitStatus(LimitStatus.NORM.name());
					mqService.submitQuotaAdjustApply(JsonUtil.toJsonString(adjustApplyDto));
				}
				// TODO 长银调额接口只支持正常状态下调额，额度状态异常只能重新授信，由于额度有效期为一年且项目周期紧急，暂时不会对线上业务影响
				//  则产品暂定方案为额度状态正常情况下进行额度调增，异常状态后续提单独提需求优化
				//额度过期，需调整额度有效期 默认当前时间添加一年
			} else if (StringUtils.equals(resultDto.getQuotaStatus(), QuotaStatus.EXPIRE.name())) {
				credit.setCreditStatus(CreditStatus.FAIL);
				credit.setRemark("额度过期，失效");
				finCreditService.updateCredit(credit);
              /*  QuotaAdjustApplyDto adjustApplyDto = new QuotaAdjustApplyDto();
                adjustApplyDto.setAdjustType(AdjustBusinessType.ADJUSTVALIDDATE.getCode());
                adjustApplyDto.setCreditId(creditId);
                adjustApplyDto.setEndDate(LocalDateTime.now().plusYears(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
                QuotaAdjustRecord quotaAdjustRecord = saveQuotaQuotaAdjustRecord(credit, resultDto, loanAmt,adjustBatchNo);
                adjustApplyDto.setAdjustId(quotaAdjustRecord.getId());
                adjustApplyDto.setOutAdjustNo(quotaAdjustRecord.getId());
                adjustApplyDto.setAdjustNo(adjustBatchNo);
                mqService.submitQuotaAdjustApply(JsonUtil.toJsonString(adjustApplyDto));*/

				//额度冻结 需解冻额度
			} else if (StringUtils.equals(resultDto.getQuotaStatus(), QuotaStatus.FROZEN.name())) {
				credit.setCreditStatus(CreditStatus.FAIL);
				credit.setRemark("额度冻结");
				finCreditService.updateCredit(credit);
             /*   QuotaAdjustApplyDto adjustApplyDto = new QuotaAdjustApplyDto();
                adjustApplyDto.setAdjustType(AdjustBusinessType.CREDITUNFROZEN.getCode());
                adjustApplyDto.setCreditId(creditId);
                QuotaAdjustRecord quotaAdjustRecord = saveQuotaQuotaAdjustRecord(credit, resultDto, loanAmt,adjustBatchNo);
                adjustApplyDto.setAdjustId(quotaAdjustRecord.getId());
                adjustApplyDto.setOutAdjustNo(quotaAdjustRecord.getId());
                adjustApplyDto.setAdjustNo(adjustBatchNo);
                mqService.submitQuotaAdjustApply(JsonUtil.toJsonString(adjustApplyDto));*/
			}
		}
	}

	private void saveCreditFlow(String creditId, String applCde, String custId) {
		CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(creditId).orElse(new CYBKCreditFlow());
		creditFlow.setCreditId(creditId);
		creditFlow.setCreditNo(applCde);
		creditFlow.setCustId(custId);
		creditFlow.setFundingModel(FundingModel.ALONE);
		cybkCreditFlowRepository.save(creditFlow);
	}

	private QuotaAdjustRecord saveQuotaQuotaAdjustRecord(Credit credit, QuotaQueryResultDto resultDto, BigDecimal loanAmt, String adjustBatchNo) {
		QuotaAdjustRecord quotaAdjustRecord = new QuotaAdjustRecord();
		QuotaCycleUserInfo quotaCycleUserInfo = quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
				credit.getChannel(), credit.getCustCertNo()).orElse(null);
		if (quotaCycleUserInfo != null) {
			quotaAdjustRecord.setCycleUserId(quotaCycleUserInfo.getId());
			quotaAdjustRecord.setUserIdCard(quotaCycleUserInfo.getUserIdCard());
			quotaAdjustRecord.setCreditSeq(quotaCycleUserInfo.getCreditSeq());
			quotaAdjustRecord.setBankCreditSeq(quotaCycleUserInfo.getBankCreditSeq());
			quotaAdjustRecord.setBankUserId(quotaCycleUserInfo.getBankUserId());
		}
		quotaAdjustRecord.setChannel(credit.getChannel());
		quotaAdjustRecord.setQueryQuotaAmt(resultDto.getCreditQuota());
		quotaAdjustRecord.setQueryBalanceQuotaAmt(resultDto.getBalanceQuota());
		quotaAdjustRecord.setQueryUsedQuotaAmt(resultDto.getUsedQuota());
		quotaAdjustRecord.setNeedQuotaAmt(loanAmt);
		quotaAdjustRecord.setAdjustStage(QuotaStage.CREDIT);
		quotaAdjustRecord.setBusinessId(credit.getId());
		quotaAdjustRecord.setStatus(ProcessStatus.INIT);
		quotaAdjustRecord.setAdjustNo(adjustBatchNo);
		return quotaAdjustRecordRepository.save(quotaAdjustRecord);
	}

	private List<LoanFile> fetchNeedDeleteFile(Credit credit) {
		List<ProjectAgreementDto> projectAgreementDtos = projectAgreementFeign.getByStage(credit.getProjectCode(), "", LoanStage.CREDIT.name());
		List<FileType> fileTypes = new ArrayList<>(projectAgreementDtos.stream().map(agreement -> FileType.valueOf(agreement.getContractTemplateType().name())).toList());
		return fetchCreditLoanFile(credit.getId(), fileTypes);
	}

	private List<LoanFile> fetchCreditLoanFile(String creditId, List<FileType> fileTypes) {
		return getCommonService().getLoanFileRepository().findByCreditIdAndFileTypeList(creditId, LoanStage.CREDIT.name(), fileTypes);
	}

	/**
	 * 每一期担保费：对客利率的等额本息还款金额-8.5%的等额本息还款金额；
	 *
	 * @param credit
	 * @return
	 */
	public CYBKGuaranteeInfo getGuaranteeAmtAndRate(CYBKGuaranteeInfo guaranteeInfo, Credit credit) {
		//计算 对客利率的等额本息还款金额
		List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
				credit.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
		BigDecimal totalAmt = repayPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
		logger.info("长银直连, 授信申请生成对客还款计划: {}", JsonUtil.toJsonString(repayPlanList));

		List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
				credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
		BigDecimal totalBankAmt = repayBankPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
		logger.info("长银直连, 授信申请生成对资还款计划: {}", JsonUtil.toJsonString(repayBankPlanList));

		BigDecimal guaranteeAmt = totalAmt.subtract(totalBankAmt); //担保费
		guaranteeInfo.setGuarAmt(guaranteeAmt);

		//担保总金额(12期) = 担保费总额 ÷ 实际期数 × 12
		guaranteeAmt = guaranteeAmt.divide(new BigDecimal(credit.getPeriods()), SIX, RoundingMode.HALF_UP).multiply(new BigDecimal(TWELVE));
		//担保费年费利率=担保费总金额/申请金额
		BigDecimal guarRate = guaranteeAmt.divide(credit.getLoanAmt(), PROCESSING_POINT_NUM, RoundingMode.HALF_UP); //平均担保费
		guaranteeInfo.setGuarRate(guarRate);

		return guaranteeInfo;
	}

	/**
	 * 授信回调处理
	 *
	 * @param creditId
	 * @param creditCallBackResultStatus
	 * @return
	 */
	public String doCreditResultCallback(String creditId, ProcessStatus creditCallBackResultStatus) {
		try {
			Credit credit = getFinCreditService().getCredit(creditId);
			//判断授信记录是否存在
			if (credit == null) {
				getWarningService().warn("回调授信记录不存在，授信creditId：" + creditId);
				return CYBKCallBackCommonResult.fail("授信记录不存在");
			}
			//落库数据终态判断
			if (credit.getCreditStatus().isFinal()) {
				//落库数据终态且与回调状态一致直接返回
				if (creditCallBackResultStatus.name().equals(credit.getCreditStatus().name())) {
					//终态一致
					return CYBKCallBackCommonResult.success();
				} else {
					//终态不一致
					getWarningService().warn("授信回调状态和已落库终态不一致，授信creditId：" + creditId + "，回调状态：" + creditCallBackResultStatus.name() + "，已落库状态：" + credit.getCreditStatus().name());
					return CYBKCallBackCommonResult.fail("授信回调状态和已落库终态不一致");
				}
			}
			//主动查询授信状态
			CYBKCreditQueryRequest creditQueryRequest = new CYBKCreditQueryRequest();
			creditQueryRequest.setOutApplSeq(credit.getId());
			creditQueryRequest.setApplCde(credit.getCreditNo());

			logger.info("长银回调授信申请查询请求:{}", JsonUtil.toJsonString(creditQueryRequest));
			CYBKCreditQueryResponse response = requestService.creditQuery(creditQueryRequest, credit.getGuaranteeCompany());
			logger.info("长银回调授信申请查询响应:{}", JsonUtil.toJsonString(response));
			CreditResultVo creditResultVo = CYBKCreditConvert.INSTANCE.toVo(response);

			//判断回调状态与主动查询状态是否一致
			if (!creditCallBackResultStatus.name().equals(creditResultVo.getStatus().name())) {
				//不一致
				getWarningService().warn("授信回调状态和主动查询状态不一致，授信creditId：" + creditId + "，回调状态：" + creditCallBackResultStatus.name() + "，主动查询状态：" + creditResultVo.getStatus().name());
				return CYBKCallBackCommonResult.fail("授信回调状态和主动查询状态不一致");
			}
			// 长银直连返回的授信编号
			CYBKCreditFlow creditFlow;

			if (credit.getCreditNo() == null) {
				//超时处理
				creditFlow = cybkCreditFlowRepository.findByCreditId(credit.getId()).orElse(null);
			} else {
				creditFlow = cybkCreditFlowRepository.findByCreditNo(credit.getCreditNo()).orElse(null);
			}
			if (ProcessStatus.SUCCESS.equals(creditResultVo.getStatus()) && creditFlow != null) {
				creditFlow.setFundingModel(FundingModel.ALONE);
				creditFlow.setCreditNo(response.getApplCde());
				creditFlow.setCustId(response.getCustId());
				cybkCreditFlowRepository.save(creditFlow);
			}
			//主动查询状态
			ProcessStatus creditResultStatus = creditResultVo.getStatus();
			// 授信结果
			credit.setCreditStatus(creditResultStatus.equals(ProcessStatus.SUCCESS) ? CreditStatus.SUCCESS : CreditStatus.FAIL);
			credit.setCreditNo(StringUtil.isBlank(credit.getCreditNo()) ? creditResultVo.getCreditNo() : credit.getCreditNo());
			credit.setCreditContractNo(creditResultVo.getCreditContractNo());
			credit.setRemark(creditResultVo.getFailMsg());
			// 授信成功金额
			credit.setCreditResultAmt(creditResultVo.getCreditResultAmt());
			if (creditResultStatus.equals(ProcessStatus.SUCCESS)) {
				if (Objects.nonNull(creditResultVo.getPassTime())) {
					credit.setPassTime(creditResultVo.getPassTime());
				}
				if (Objects.nonNull(creditResultVo.getCapExpireTime())) {
					credit.setCapExpireTime(creditResultVo.getCapExpireTime());
				}
			}
			// ext授信可用额度
			if (creditResultVo.getStatus().equals(ProcessStatus.SUCCESS)) {
				getCommonService().initCreditAvailable(credit);
			}
			// 授信结果
			finCreditService.updateCredit(credit);
			return CYBKCallBackCommonResult.success();
		} catch (Exception e) {
			logger.error("授信回调异常：" + e);
			return CYBKCallBackCommonResult.fail();
		}
	}

	private String toCYBKAddress(String address) {
		try {
			String rightOne = StringUtils.right(address, RIGHT_ONE);
			if (TAIL_PADDING_LIST.contains(rightOne)) {
				// 末尾增加"组"
				return address.concat(PADDING_CHAR);
			}
		} catch (Exception e) {
			logger.warn("长银直连地址转换异常:", e);
			return address;
		}

		return address;
	}

	public String getCreditLevel(String acardScore) {
		if (StringUtils.isNotBlank(acardScore)) {
			int score = Integer.parseInt(acardScore);
			if (score <= oneThousand && score > fourHundred) {
				return "A";
			} else if (score <= fourHundred && score > threeHundred) {
				return "B";
			} else if (score <= threeHundred && score > twoHundred) {
				return "C";
			} else {
				return "D";
			}
		} else {
			return "D";
		}
	}

	/**
	 * @return 是否复借 否：0 是：1
	 */
	private String ifReloan() {
		int rd = RandomUtils.nextInt(ONE, TEN);
		if (rd >= ONE && rd <= FOUR) {
			return "1";
		}
		return "0";
	}

	@Override
	protected Credit bankCreditValidate(CreditApplyVo<ExtInfoVo> applyVo) { // 此bankChannelType 是富民的
		// 去长银直连前, 本地前筛校验
		if (!cybkLocalValid.validCreditAmount(applyVo.getCreditAmt())) {
			throw new BizException(BizErrorCode.CREDIT_AMOUNT_LIMIT);
		}

		if (!cybkLocalValid.validPeriods(applyVo.getPeriods())) {
			throw new BizException(BizErrorCode.CREDIT_PERIODS_LIMIT);
		}

//        LocalTime actTime = LocalTime.now();
//        if (!cybkLocalValid.isValidTime(actTime)) {
//            throw new BizException(BizErrorCode.CREDIT_TIME_INVALID);
//        }

		String certNo = applyVo.getIdCardInfo().getCertNo();
		if (!cybkLocalValid.isValidAge(certNo)) {
			throw new BizException(BizErrorCode.CREDIT_AGE_LIMIT);
		}

		// 可授信
		Credit credit = getCommonService().creditApply(applyVo);
		credit.setCreditType(CreditType.NORMAL_CREDIT);
		return creditRepository.save(credit);
	}

	/**
	 * 证件有效转换
	 *
	 * @param expireDay
	 * @return
	 */
	public LocalDate calcCertValidDate(LocalDate expireDay) {
		if (ID_CARD_EXPIRE_PERMANENT.equals(expireDay)) {
			return CYBK_PERMANENT;
		}
		return expireDay;
	}

	@Override
	protected String getProductType() {
		return null;
	}

	@Override
	public boolean isSupport(BankChannel channel) {
		return BankChannel.CYBK == channel;
	}

	@Autowired
	public void setCybkLocalValid(CYBKLocalValid cybkLocalValid) {
		this.cybkLocalValid = cybkLocalValid;
	}

	@Autowired
	public void setRequestService(CYBKRequestService requestService) {
		this.requestService = requestService;
	}

	@Autowired
	public void setAccountRepository(AccountRepository accountRepository) {
		this.accountRepository = accountRepository;
	}

	@Autowired
	public void setAccountContactInfoRepository(AccountContactInfoRepository accountContactInfoRepository) {
		this.accountContactInfoRepository = accountContactInfoRepository;
	}

	@Autowired
	public void setAgreementSignatureRepository(AgreementSignatureRepository agreementSignatureRepository) {
		this.agreementSignatureRepository = agreementSignatureRepository;
	}

	public WarningService getWarningService() {
		return warningService;
	}
}
